{"name": "thsjj-jj-falcon-fenshifundtab-docker", "version": "0.1.0", "author": "<EMAIL>", "jira": "IJIJIN-8553", "private": true, "scripts": {"start": "npm run dev:web", "start:web": "npm run dev:web", "start:hummer": "npm run dev:hummer", "dev:web": "kingfisher start --target web --framework vue --mode dev", "dev:hummer": "kingfisher start --target hummer --mode dev", "build:web": "kingfisher build --target web --framework vue", "build:hummer": "kingfisher build --target hummer --framework vue"}, "dependencies": {"@hummer/hummer-fetch": "^0.0.3", "@hummer/hummer-front": "^1.0.8", "@king-fisher/apis": "^0.5.9", "@king-fisher/app-vue": "^0.5.6", "@king-fisher/env": "^0.5.4", "@king-fisher/vue-components": "0.5.26", "@king-fisher/vue-plugin-canvas": "^0.5.1", "@king-fisher/falcon": "0.5.12-beta14", "archiver": "^5.3.1", "babel-plugin-lodash": "^3.3.4", "better-scroll": "^2.4.2", "core-js": "^3.6.5", "lodash": "^4.17.21", "lodash-webpack-plugin": "^0.11.6", "vue-router": "^3.6.5"}, "devDependencies": {"@king-fisher/cli": "^0.5.9", "@king-fisher/vue-webpack": "0.5.11", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-typescript": "~4.5.15", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "eslint": "^6.7.2", "eslint-config-sonarjs": "^1.1.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "prettier": "^2.2.1", "typescript": "~4.1.5", "vue-template-compiler": "^2.6.11"}}