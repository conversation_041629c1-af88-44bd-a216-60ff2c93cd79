const path = require('path');
export default defineBuildConfig({
  // output: {
  //   path: path.resolve(__dirname, './dist'),
  //   filename: '[name].js',
  // },
  alias: {
    '@': path.join(__dirname, './src'),
    '@api': path.join(__dirname, './src/apis'),
    '@img': path.join(__dirname, './src/assets/images'),
    '@comp': path.join(__dirname, './src/components'),
    '@mixin': path.join(__dirname, './src/mixins'),
    '@utils': path.join(__dirname, './src/utils'),
  },
  loader: {
    asset: {
      maxSize: 2048,
    }
  },
  importOnDemand: [
    {
      // tsconfig配置别名以获取提示，还需要设置alias
      libraryName: 'importTest',
      // {buildTarget}占位符，会编译成hummer/web，不设置libraryDirectory默认为lib/{buildTarget}
      libraryDirectory: '{buildTarget}',
      // 效果与上方一致
      // customName(buildTarget: 'web' | 'hummer') {
      //   return buildTarget;
      // },
    },
    {
      libraryName: '@king-fisher/falcon',
      libraryDirectory: 'lib/es/{buildTarget}',
    },
    // {
    //   // 解决apis版本过低兼容问题
    //   libraryName: '@king-fisher/apis',
    //   libraryDirectory: 'lib/es/{buildTarget}',
    // }
  ],
  /* MONITOR: {{ REGISTER_INFO }} */
});
