/*
 * <AUTHOR>
 * @Date 2022-05-20 17:07:53
 * @LastEditTime 2022-08-31 13:53:32
 */
module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    'sonarjs',
    'plugin:vue/essential',
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/prettier/@typescript-eslint',
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  globals: {
    NODE_DEBUG_ENV: true,
    Hummer: true,
    HXJsBridge: true,
    UnifiedRequestBridge: true,
    __GLOBAL__: true,
    _t: true,
  },
  rules: {
    indent: [2, 2, { SwitchCase: 1 }],
    'no-magic-numbers': [1, { ignore: [0, -1, 1] }],
    'no-extra-parens': 0,
    'vue/multi-word-component-names': 'off',
    camelcase: 0,
    'no-invalid-this': 0,
    'prefer-rest-params': 2,
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    'vue/script-setup-uses-vars': 'off',
    'no-unreachable-loop': 'off',
    'default-case-last': 'off',
  },
};
