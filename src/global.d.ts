///  <reference types="@king-fisher/vue-webpack" />
/**
 * 用于描述拓展组件定义
 * 下面是一个定义示例
 *   declare class Demo {
 *     name: string;
 *     sayName(): void;
 *   }
 *
 * 使用如下:
 * 使用global.d.ts 声明的类
 *   let demo = new Demo()
 *   demo.sayName()
 */
declare module '*.png';
declare module '*.jpeg';
declare module '*.jpg';
declare module '*.gif';

declare const __GLOBAL__: any;
declare const HXJsBridge: any;
declare const Hummer: any;

declare interface EnvItem {
  Host: string;
  router: { [key: string]: string };
  api: { [key: string]: string };
  url: { [key: string]: string };
}

type numStr = number | string;
