const release: EnvItem = {
  Host: '',
  router: {
    relatedETFTab: 'relatedETFTab.js',
  },
  api: {},
  url: {
    HotETF: 'https://eq.10jqka.com.cn/open/api/etf_rank/v1/hot.txt',
    // 板块相关场外基金http接口
    PlateOutFund: 'https://fund.10jqka.com.cn/quotation/open/api/select/tool/v1/query/plateOutFund',
    // 指数相关场外基金列表
    indexOutFund: 'https://fund.10jqka.com.cn/quotation/open/api/select/tool/v1/query/indexOutFund',
    // 期货相关场外基金列表
    futuresOutFund: 'https://fund.10jqka.com.cn/quotation/etf_tab/hq_tab/v1/related_out_fund/futures_fund',
    // 现货相关场外基金列表
    spotOutFund: 'https://fund.10jqka.com.cn/quotation/etf_tab/hq_tab/v1/related_out_fund/spot_fund',
    // 港股板块相关场外基金列表
    hkPlateOutFund: 'https://fund.10jqka.com.cn/quotation/etf_tab/hq_tab/v1/related_out_fund/block_fund_HK',
    YanXuanFund: 'https://fund.10jqka.com.cn/hqapi/static/hq/commonconfig/kvdatasave/hyzt',
    YanXuanFundZDF: 'https://fund.10jqka.com.cn/quotation/fund/v1/card/info',
    EducateUrl: 'https://t.10jqka.com.cn/pid_225711417.shtml',
    // 相关ETF跳转文案和链接
    etfTipConfig: 'https://fund.10jqka.com.cn/hqapi/static/hq/commonconfig/kvdatasave/normal_config_etfchangjingyypz',
    // 场内基金跳转文案和链接
    fundTipConfig: 'https://eq.10jqka.com.cn/open/api/dynamic_configuration/v1/get_config?ids=6225',
    // 相关场内基金列表
    RelatedInnerFund: 'https://fund.10jqka.com.cn/quotation/etf_tab/hq_tab/v1/related_fund_list',
    // 走aime推荐引擎的问财服务
    AIRecommend: 'https://dq.10jqka.com.cn/fuyao/fundtab_trans/etf/v2/ai_chat/recommend'
  },
};

const config: { [keys: string]: EnvItem } = { release };
export default config['release'];
