/**
 * 判断是否为数字
 *  @param {unknown} obj 任意类型
 *  @returns {Boolean}
 */
export function isNumber(obj?: unknown) {
  return typeof obj === 'number' && !isNaN(obj);
}

/**
 * 判断是否为Null
 *  @param {unknown} val 任意类型
 *  @returns {Boolean}
 */
export function isNull(val?: unknown) {
  const target = typeof val === 'object' ? Object.prototype.toString.call(val) : `${val}`;
  return /null|undefined|nan|^\s*$/i.test(target);
}

/**
 * 判断是否为Object类型 注意null属于Object
 *  @param {unknown} object 任意类型
 *  @returns {Boolean}
 */
export function isObj(object?: unknown) {
  return (
    object &&
    typeof object === 'object' &&
    Object.prototype.toString.call(object).toLowerCase() === '[object object]'
  );
}

export function isValidSrting(obj: unknown) : boolean {
  if (typeof obj !== 'string') {
    return false;
  }
  const tmp = obj.trim();
  return tmp.length > 0;
}

export function isValidArray(obj: unknown) : boolean {
  return Array.isArray(obj) && obj.length > 0;
}

const DELAY = 500;

/**
 * @function throttle
 * @param {Function} func
 * @param {Number} intervalTime
 * @returns {Function}
 */
export function throttle(
  func: {
    apply: (arg0: unknown, ...arg1: unknown[]) => void;
  },
  intervalTime = DELAY
) {
  let flag = true;
  return function (this: unknown, ...args: unknown[]) {
    if (flag) {
      func.apply(this, args);
      flag = false;
      setTimeout(() => {
        flag = true;
      }, intervalTime);
    }
  };
}

/**
 * @function debounce
 * @param {Function} func
 * @param {Number} intervalTime
 * @returns {Function}
 */
export function debounce(
  func: {
    apply: (arg0: unknown, arg1: unknown[]) => void;
  },
  intervalTime = DELAY
) {
  let timeId: number | null = null;
  return function (this: unknown, ...args: unknown[]) {
    if (timeId) {
      clearTimeout(timeId);
    }
    timeId = setTimeout(() => {
      timeId = null;
      func.apply(this, args);
    }, intervalTime);
  };
}

/**
 * 封装 JSON.parse() 若有错误抛出位置
 *  @param {any} name Error.name
 *  @param {any} message Error.message
 *  @returns {Error}
 */
export function jsonParseThrow(params: string, place: string) {
  try {
    return JSON.parse(params);
  } catch (err) {
    throw new HxError('JSON.parse error', JSON.stringify({ place, params }));
  }
}

//  把多位数转化保留decimalPlaces位小数的加万或者亿字符串
const M_ZERO = 0;
const M_TEN_THOUSAND = 10000;
const M_HUNDRED_MILLION = 100000000;
const TOFIXEDNUM = 2;
export function formatAmount(value: any, decimalPlaces = TOFIXEDNUM) {
  const symb = value >= M_ZERO;
  let p = parseFloat(value);
  if (isNaN(p)) {
    return '';
  }
  if (p < M_ZERO) {
    p = Math.abs(p);
  }
  if (p >= M_HUNDRED_MILLION * M_TEN_THOUSAND) {
    return `${symb ? '' : '-'}${(p / M_HUNDRED_MILLION / M_TEN_THOUSAND).toFixed(
      decimalPlaces
    )}万亿`;
  }
  if (p >= M_HUNDRED_MILLION) {
    return `${symb ? '' : '-'}${(p / M_HUNDRED_MILLION).toFixed(decimalPlaces)}亿`;
  }
  if (p >= M_TEN_THOUSAND) {
    return `${symb ? '' : '-'}${(p / M_TEN_THOUSAND).toFixed(decimalPlaces)}万`;
  }
  return `${symb ? '' : '-'}${p.toFixed(decimalPlaces)}`;
}

// 处理字符串型的数字，正数添加添加正号
export function formatNumber(str: string) {
  if (isNull(str) || str === '--') {
    return {
      sign: '',
      abs: '--',
      num: '--',
      signStr: '--',
      colorClass: 'grey',
    };
  }
  let sign = '';
  let signStr = str;
  let abs = 0;
  let num = 0;
  let colorClass = 'grey';
  num = parseFloat(str);
  if (num > 0) {
    sign = '+';
    signStr = `+${str}`;
    colorClass = 'red';
  } else if (num < 0) {
    sign = '-';
    colorClass = 'green';
  } else {
    signStr = str.replace(/0+[.]?0*/, '0.00');
  }
  abs = Math.abs(num);
  return {
    abs: isNaN(abs) ? '' : abs,
    num: isNaN(num) ? 0.0 : num,
    sign,
    signStr,
    colorClass,
  };
}
