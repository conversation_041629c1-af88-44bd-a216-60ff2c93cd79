import falcon from '@king-fisher/falcon';

/**
 * 获取所有cookie
 * * @returns {String}
 */
export function getCookie() {
  return HXJsBridge.getUserCookie();
}

/**
 * 获取userId
 * * @returns {String}
 */
export function getUserId() {
  const a = getCookie();
  if (!a) {
    return 0;
  }
  let b = 0;
  if (0 !== a.length) {
    for (let c = a.split(';'), d = 0; d < c.length; d++) {
      const e = c[d].split('=');
      'userid' === e[0].trim() && (b = e[1] || 0);
    }
  }
  return b;
}

/**
 * 跳转客户端股票分时页
 * @param {string | number} stockCode 股票代码
 * @param {string} marketId 市场id
 */
export function jumpToFenShi(
  stockCode: string | number,
  marketId: string,
  config: {
    codeList?: string[] | number[],
    marketList?: string[],
    nameList?: string[]
  } = {},
): void {
  if (!stockCode || !marketId) {
    return;
  }
  let url = `client://client.html?action=ymtz^webid=2205^stockcode=${stockCode}^marketid=${marketId}`;
  const { codeList, marketList, nameList } = config;
  if (codeList && marketList && nameList) {
    url += `^codelist=${codeList.join(',')}^marketlist=${marketList.join(
      ','
    )}^namelist=${nameList.join(',')}`;
  }
  HXJsBridge.jumpNativePage(url);
}


export enum EventTracingAction {
  /// 点击
  click = 0,
  /// 滑动
  slide = 1,
  /// 展示
  show = 2,
  /// 悬浮
  hover = 3,
  /// 停留
  stay = 4,
  /// 曝光消失
  dis = 5,
  /// 拉动
  pull = 6,
  /// 双击
  dclick = 7,
  /// 应用启动
  start = 8,
  /// 长按
  press = 9,
  /// 应用关闭
  end = 10,
}

/**
 * @description: 新版埋点发送平台
 * @param eventId 埋点id
 * @param actionType 行为类型，详见枚举注释
 * @param logMap 扩展字段，字典的形式传入，支持为空
 * @param isAutoSign 是否自动拼接应用前缀
 * @return {*}
 */
export function sendEventCaptureRecord (
  eventId: string,
  actionType: EventTracingAction,
  logMap?: { [key in string]: unknown },
  isAutoSign = false
) {
  return new Promise((resolve, reject) => {
    falcon.eventCaptureRecord({
      eventId,
      actionType,
      logMap,
      isAutoSign,
      success(res) {
        resolve(res);
      },
      fail(res) {
        reject(res);
      },
    });
  });
}
/**
 * 跳转手炒2804网页，新打开一个页面
 * @param url 网页链接
 */
export function jumpToNewWebPage(url: string): void {
  if (!url) {
    return;
  }
  const clientWebPage = 'client.html?action=ymtz^webid=2804';
  HXJsBridge.jumpNativePage(`${clientWebPage}^url=${url}^mode=new`);
}
