import Vue from 'vue';
import App from './App.vue';

Vue.config.productionTip = false;

import VueRouter from 'vue-router';
Vue.use(VueRouter);

const routeCode = [
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "chunk-home"*/ './pages/chart/Home.vue'),
  },
  {
    path: '/about',
    name: 'About',
    component: () => import(/* webpackChunkName: "chunk-about"*/ './pages/chart/About.vue'),
  },
];
const router = new VueRouter({
  routes: routeCode,
});

new Vue({
  router,
  render: h => h(App),
}).$mount('#app');
