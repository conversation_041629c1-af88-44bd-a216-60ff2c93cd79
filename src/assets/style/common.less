.rise-font {
  font-family: THSJinRongTi-Medium;
}

.posi-r {
  position: relative;
}

.posi-a {
  position: absolute;
}

.no-wrap {
  white-space: nowrap;
}

.break-all {
  word-break: break-all;
}

// 文本超出显示...
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// flex
.flex {
  display: flex;
}

.flex-d-c {
  flex-direction: column;
}

.flex-d-r {
  flex-direction: row;
}

.ai-c {
  align-items: center;
}

.jc-c {
  justify-content: center;
}

.jc-sb {
  justify-content: space-between;
}

.jc-sa {
  justify-content: space-around;
}

.jc-fs {
  justify-content: flex-start;
}

.jc-fe {
  justify-content: flex-end;
}

.ai-fs {
  align-items: flex-start;
}

.ai-fe {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
  /* flex: 1 1 auto; */
}

.ta-c {
  text-align: center;
}

.ta-r {
  text-align: right;
}
