//外边距 - 上
.mt-2{
  margin-top: 2px;
}
.mt-4{
  margin-top: 4px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-14 {
  margin-top: 14px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-24 {
  margin-top: 24px;
}

.mt-26 {
  margin-top: 26px;
}

.mt-28 {
  margin-top: 28px;
}

.mt-32 {
  margin-top: 32px;
}

.mt-40 {
  margin-top: 40px;
}

//外边距 - 下
.mb-8 {
  margin-bottom: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-28 {
  margin-bottom: 28px;
}

.mb-32 {
  margin-bottom: 32px;
}

.mb-36 {
  margin-bottom: 36px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-44 {
  margin-bottom: 44px;
}

//外边距 - 右
.mr-8 {
  margin-right: 08px;
}

.mr-12 {
  margin-right: 12px;
}

.mr-16 {
  margin-right: 16px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-24 {
  margin-right: 24px;
}

.mr-32 {
  margin-right: 32px;
}

.mr-n12 {
  margin-right: 12;
}

//外边距 - 左
.ml-4 {
  margin-left: 4px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-12 {
  margin-left: 12px;
}

.ml-16 {
  margin-left: 16px;
}

.ml-18 {
  margin-left: 18px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-24 {
  margin-left: 24px;
}

.ml-32 {
  margin-left: 32px;
}

.ml-n12 {
  margin-left: 12;
}

.pt-24 {
  padding-top: 24px;
}

.pb-32 {
  padding-bottom: 32px;
}

// 内边距-左
.pl-16 {
  padding-left: 16px;
}

.pl-32 {
  padding-left: 32px;
}

.pr-16 {
  padding-right: 16px;
}
.pr-32 {
  padding-right: 32px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pb-18 {
  padding-bottom: 18px;
}
