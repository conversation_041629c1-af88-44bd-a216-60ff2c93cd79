<template>
  <view :class="['page', theme]" ref="root">
    <view ref="container">
      <AIRobot v-if="showAIRobot" :theme="theme" :config="AIConfig"></AIRobot>
      <view>
        <RelatedFund v-if="showInnerRelated" ref="relatedInnerList" :dataList="firstPageRelatedFunds"
          :headerList="relatedInnerHeaderList" moduleName="相关ETF" rightText="ETF专区" :pageId="pageId"
          :stock-code="stockCode" :market="marketId" :user-visible-stock-code="userVisibleStockCode" :statModuleName="statModuleName"
          type="inner">
        </RelatedFund>
        <RecommondFund v-if="showInnerRecommend" ref="recommendInnerList" :dataList="recommendInnerList"
          moduleName="相关ETF" hotText="大家都在关注" moreTitle="查看ETF热榜" type="inner" :pageId="pageId"
          :statModuleName="statModuleName" :stock-code="stockCode" :user-visible-stock-code="userVisibleStockCode"
          :emptyConfig="{ title: '暂无相关ETF', showMore: true, moreText: '去ETF专区，一站式选ETF' }">
        </RecommondFund>
      </view>
      <view v-if="outerRelatedModuleShow">
        <RelatedFund v-if="showOuterRelated" ref="relatedOuterList" :dataList="relatedOuterList"
          :headerList="relatedOuterHeaderList" :showWeakTips="showWeakTips" :matchType="matchType" :pageId="pageId"
          :stock-code="stockCode" :market="marketId" :user-visible-stock-code="userVisibleStockCode" :statModuleName="statModuleName"
          moduleName="相关场外基金" type="outer">
        </RelatedFund>
        <RecommondFund v-if="showOuterRecommend" ref="recommendOuterList" :dataList="recommendOuterList"
          moduleName="相关场外基金" hotText="为你严选" moreTitle="发现更多" type="outer" :pageId="pageId" :stock-code="stockCode"
          :user-visible-stock-code="userVisibleStockCode" :statModuleName="statModuleName"
          :emptyConfig="{ title: '暂无相关场外基金', showMore: false }">
        </RecommondFund>
      </view>
      <BottomView v-if="isInited" :theme="theme"></BottomView>
    </view>
  </view>
</template>

<script>
import { HomeViewModel } from '@/components/viewModel/homeViewModel.js';
import AIRobot from '@/components/AIRobot/index.vue';
import RelatedFund from '@/modules/RelatedFund/index.vue';
import RecommondFund from '@/modules/RecommondFund/index.vue';
import BottomView from '@/components/BottomView/index.vue';
import { store } from '@/lib/store';
import lifeTimeMixin from '@/mixins/lifeTimeMixin';
import relatedFundMixin from '@/mixins/relatedFundMixin';
import firstPageDataMixin from '@/mixins/firstPageDataMixin';
import indexDataRequestMixin from '@/mixins/indexDataRequestMixin';
import { isPlate } from '@/lib/tools';
import { getFirstSceneType, getSecondSceneType } from '@/lib/SceneType';
import { SceneFirstType } from '@/lib/SceneTypeDefines';
import { headerSimilarity, headerPosition, headerVolume, headerScale, headerUnit, premiumRate } from '@/lib/constant';

export default {
  pageConfig: {
    canScroll: false,
  },
  mixins: [lifeTimeMixin, relatedFundMixin, firstPageDataMixin, indexDataRequestMixin],
  components: {
    AIRobot,
    RelatedFund,
    RecommondFund,
    BottomView
  },
  data() {
    return {
      theme: '',
      pageHeight: 600,
      stockCode: '',
      stockName: '',
      marketId: '',
      pageId: '2267',
      isInited: false,
      isNotifiedViewDidShow: false,
      viewDidHide: false,
      showWeakTips: false,
      showInnerRelated: false,
      showInnerRecommend: false,
      showOuterRelated: false,
      showOuterRecommend: false,
      hummerContainerId: '',
      viewModel: null,
      relatedOuterList: [],
      recommendInnerList: [],
      recommendOuterList: [],
      matchType: '',
      AIConfig: {
        query: '',
        rec_info: '',
        trace_id: '',
        jump_url: '',
      },
      // 埋点页面名称
      statModuleName: 'fenshi_jijintab',
      // 当前显示页面的 stockCode
      userVisibleStockCode: '',
    };
  },
  computed: {
    showAIRobot() {
      return this.AIConfig.query;
    },
    isDarkTheme() {
      return this.theme === 'dark';
    },
    isBanKuai() {
      return isPlate(this.marketId);
    },
    // 场内基金标题栏
    relatedInnerHeaderList() {
      let headerList;
      if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
        headerList = [headerPosition, headerVolume];
      } else if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        headerList = [headerSimilarity, headerPosition];
      } else {
        headerList = [premiumRate, headerVolume];
      }
      return headerList;
    },
    // 场外基金标题栏
    relatedOuterHeaderList() {
      if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        return [headerScale, headerPosition];
      } else {
        return [headerScale, headerUnit];
      }
    },
    outerRelatedModuleShow() {
      return store.firstSceneType !== SceneFirstType.hkStock && store.firstSceneType !== SceneFirstType.usStock;
    }
  },
  created() {
    if (_t.isGrayReleaseOn('HQ_FenshiKlineSameScreen')) {
      this.statModuleName = 'newfenshi_fenshi_jijintab';
    }
  },
  onLoad() {
    //安卓端由于页面复用，为保证页面刷新，页面切换就会重新onLoad，但是onHide之后不需要
    if (this.viewDidHide && !__GLOBAL__.isIos) {
      return;
    }
    this.isInited = false;
    this.showInnerRelated = false;
    this.showInnerRecommend = false;
    this.showOuterRelated = false;
    this.showOuterRecommend = false;

    this.loadEventListener();

    //获取页面高度，当展示内容不到一页的时候展示一页的高度
    this.getPageHeight();

    const { stockCode, marketId, stockName, hummerContainerId } = Hummer.pageInfo.params;
    store.stockCode = stockCode;
    store.marketId = marketId;
    store.stockName = stockName;
    store.firstSceneType = getFirstSceneType(marketId)
    store.secondSceneType = getSecondSceneType(marketId, stockCode)
    this.stockCode = stockCode;
    this.marketId = marketId;

    this.hummerContainerId = hummerContainerId ? hummerContainerId : '';
    this.viewModel = new HomeViewModel();

    this.viewModel.initHummerContext(this.hummerContainerId);

    this.requestFirstPageData(marketId, stockCode, store.firstSceneType);
    this.requestPlateOutFund();
    this.requestAIData();
  },
  onShow() {
    const { stockCode, marketId, stockName } = Hummer.pageInfo.params;
    //iOS下非分时同屏页面左右切换但是HummerView复用，故使用onShow刷新页面
    if (store.stockCode !== stockCode && __GLOBAL__.isIos) {
      store.stockCode = stockCode;
      store.marketId = marketId;
      store.stockName = stockName;
      store.firstSceneType = getFirstSceneType(marketId);
      store.secondSceneType = getSecondSceneType(marketId, stockCode);

      this.marketId = marketId;
      this.isInited = false;
      this.showInnerRelated = false;
      this.showInnerRecommend = false;
      this.showOuterRelated = false;
      this.showOuterRecommend = false;

      this.requestFirstPageData(marketId, stockCode, store.firstSceneType);
      this.requestPlateOutFund();
      this.requestAIData();
    }
    this.userVisibleStockCode = this.stockCode;
    this.viewDidHide = false;
    //如果已经请求到接口数据
    if (this.isInited && !this.isNotifiedViewDidShow) {
      this.notifiViewDidShowEvent();
    }
    this.isNotifiedViewDidShow = true;
  },
  onHide() {
    this.viewDidHide = true;
    this.userVisibleStockCode = '';
    this.isNotifiedViewDidShow = false;
  },
  methods: {
    getPageHeight() {
      // 误差调整
      let padding = 20;
      if (__GLOBAL__.isIos) {
        padding = 0;
      }
      //底部bar高度
      const androidTabBottomHeight = 40;
      const iosTabBottomHeight = 49;
      const tabBottomHeight =
        Hummer.env.platform === 'iOS' ? iosTabBottomHeight : androidTabBottomHeight;
      const scrollBarHeight = 40;
      this.pageHeight = __GLOBAL__.screenHeight - Hummer.env.safeAreaBottom - tabBottomHeight - _t.statusBarAndNavigationHeight() - scrollBarHeight - padding;
    },
    heightChange(height) {
      const changedHeight = height < this.pageHeight ? this.pageHeight : height;
      this.viewModel.viewHeightDidChanged(changedHeight);
      this.$refs.root.parent.element.style = {
        height: changedHeight,
      };
    },
    refreshViewHeight() {
      //数据赋值跟视图绘制存在一定的时间差
      const delay = 100;
      setTimeout(() => {
        this.$refs.container.element.getRect(res => {
          this.heightChange(res.height);
        });
      }, delay);
    },
  },
};
</script>
<style lang="less">
@import '../../assets/style/index.less';

.page {
  background-color: #f5f5f5;
  width: 100%;
  flex: 1;
}

.page.dark {
  background-color: #0f0f0f;
}

.navigation {
  position: absolute;
  top: 0;
  left: 0;
}

.text-color {
  color: black;
  font-size: 16;
}
</style>
