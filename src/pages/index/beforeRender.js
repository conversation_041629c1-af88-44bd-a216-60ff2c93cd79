import tools from '@/lib/tools.js';
import { Button, View, Text, Image, Scroller } from '@king-fisher/vue-components';

import config from '@/config';

export default Vue => {
  Vue.component('k-button', Button);
  Vue.component('k-view', View);
  Vue.component('k-text', Text);
  Vue.component('k-image', Image);
  Vue.component('k-scroller', Scroller);

  __GLOBAL__.isIos = Hummer.env.platform === 'iOS';
  __GLOBAL__.screenWidth = Number(Hummer.env.deviceWidth);
  __GLOBAL__.screenHeight = Number(Hummer.env.deviceHeight);
  __GLOBAL__._t = tools;

  Object.assign(__GLOBAL__, config);
};
