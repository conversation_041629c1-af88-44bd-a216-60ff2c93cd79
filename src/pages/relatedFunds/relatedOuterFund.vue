<template>
  <k-view>
    <HqTable v-if="!showEmpty" :request-type=2 :headerFieldIds="headerFieldIds" :headerFieldNames="headerFieldNames"
      :sortIdProp="sortId" :sortOrderProp="sortOrder" :buildHttpUrl="buildHttpUrl" :onReceiveHttpResult="parseHttpRes"
      :onItemClick="handleItemClick" :statPrefix="statPrefix" :showPageInfo="true" :theme="theme" :height="tableHeight"
      @onParseEmpty="onParseEmpty" @updated-table-data="updatedTableData" @onHandleSort="onHandleSort" />
    <RecommondFund v-else ref="recommendOuterList" :dataList="recommendOuterList" moduleName="" hotText="为你严选" moreTitle="发现更多"
      type="outer" :pageId="pageId" :statModuleName="statModuleName"
      :emptyConfig="{ title: '暂无相关场外基金', showMore: false }">
    </RecommondFund>
  </k-view>
</template>

<script lang="ts">
import RelatedFundsBanner from './relatedFundsBanner.vue';
import HqTable from '@/components/hqTable/hqTable.vue';
import { TableData } from '@/components/hqTable/hqTable';
import { getValueColor, getSubValueColor, getZdfColor, fix2DicimalPercent, formatAmount, fixDecimals } from '@/components/hqTable/hqTool';
import { HqParams, ParsedDataModel, TableRowEntity, TableCellEntity } from '@/components/hqTable/hqTable';
import { defineComponent } from '@king-fisher/app-vue';
import { sendClientCbas, isPlate } from '@/lib/tools';
import { getOuterFundUrl } from '@/lib/common';
import { ActionOperaType, fieldConfig } from '@/lib/constant';
import RecommondFund from '@/modules/RecommondFund/index.vue';
import dataUtilsMixin from '@/mixins/dataUtilsMixin';
import indexDataRequestMixin from '@/mixins/indexDataRequestMixin';
import { sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';
import { getFirstSceneType, getSecondSceneType } from '@/lib/SceneType'
import { FuturesSceneSecondType, SceneFirstType } from '@/lib/SceneTypeDefines';
import { store } from '@/lib/store';

// srotId和对应Key的对应关系
const fieldMap: { [key: string]: any } = {
  36164: riseSortIdKey(),
  36151: 'fundPerformanceSimilarity',
  36152: fundInvestRateKey(),
  36157: 'fundScale',
  36156: 'unitNav',
  36158: 'week',
  36159: 'month',
  36160: 'tmonth',
  36161: 'hyear',
  36163: 'nowyear',
  36162: 'year',
};

function riseSortIdKey() {
  const { marketId } = Hummer.pageInfo.params;
  const firstType = getFirstSceneType(marketId);
  if (firstType === SceneFirstType.spot || firstType === SceneFirstType.futures || firstType === SceneFirstType.hkPlate) {
    return 'chgpct'
  }
  return 'riseLaster'
}

function fundInvestRateKey() {
  const { marketId } = Hummer.pageInfo.params;
  if (getFirstSceneType(marketId) === SceneFirstType.hkPlate) {
    return 'block_fund_holdrate'
  }
  return 'fundInvestRate'
}

export default defineComponent({
  props: {
    stockCode: {
      type: String,
      required: true,
    },
    marketId: {
      type: String,
      required: true,
    },
    sortId: {
      type: String,
      default: '',
      required: true,
    },
    sortOrder: {
      type: String,
      default: '0',
      required: true,
    },
    theme: {
      type: String,
      default: '',
    },
    relateViewHeight: {
      type: Number,
      required: true,
    },
    statModuleName: String,
  },
  components: { RelatedFundsBanner, HqTable, RecommondFund },
  mixins: [dataUtilsMixin, indexDataRequestMixin],
  data() {
    return {
      statPrefix: '',
      recommendOuterList: [],
      showEmpty: false
    };
  },
  computed: {
    isDarkTheme() {
      return this.theme === 'dark';
    },
    headerFieldIds() {
      if (getSecondSceneType(this.marketId, this.stockCode) === FuturesSceneSecondType.bondFutures) {
        return fieldConfig.fundBondFuturesFieldIds;
      }
      if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        return fieldConfig.fundPlateFieldIds;
      }
      return fieldConfig.fundIndexFieldIds; 
    },
    headerFieldNames() {
      if (getSecondSceneType(this.marketId, this.stockCode) === FuturesSceneSecondType.bondFutures) {
        return fieldConfig.fundBondFuturesFieldNames;
      }
      if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        return fieldConfig.fundPlateFieldNames;
      }
      return fieldConfig.fundIndexFieldNames;
    },
    tableHeight() {
      return this.relateViewHeight;
    },
    isBanKuai() {
      return isPlate(this.marketId);
    }
  },
  methods: {

    // 生成http行情请求完整url
    buildHttpUrl(param?: HqParams): string {
      let {sortId, sortOrder} = this;
      let startRow = 0;
      if (param) {
        sortId = param.sortId || this.sortId;
        sortOrder = param.sortOrder || this.sortOrder;
        startRow = param.startRow || 0;
      }
      const urlPrefix = getOuterFundUrl(this.marketId);
      return `${urlPrefix}?code=${this.stockCode}&market=${this.marketId}&sortId=${fieldMap[sortId]}&sortOrder=${sortOrder}&startRow=${startRow}&rowCount=20`;
    },

    // 解析http返回数据
    parseHttpRes(res: any): ParsedDataModel {
      const resData = res?.data?.data;
      let table;
      if (resData) {
        const codeData = resData[this.stockCode];
        if (codeData) {
          table = codeData.table;
        }
      }
      if (!table) {
        // 解析失败
        return {
          handled: true,
          flag: -1,
        };
      }

      const {row, total, data} = table;

      if (!data) {
        // 解析失败
        return {
          handled: true,
          flag: -1,
        };
      }

      const list = this.parseDataArr(data, row);

      return {
        handled: true,
        flag: 0,
        data: {
          startIndex: row || 0,
          total,
          list,
        },
      };
    },

    // 解析数据列表
    parseDataArr(data: any, startRow: number): TableRowEntity[] {
      const rowList: TableRowEntity[] = [];

      data.forEach((element: any, index: number) => {
        const rowData = this.parseRowData(element, startRow + index);
        rowList.push(rowData);
      });

      return rowList;
    },

    // 解析单行数据，row: 当前行在总列表中序号
    parseRowData(rowData: any, rowIndex: number): TableRowEntity {
      const cellList: TableCellEntity[] = [];

      // 第一列，名称代码
      cellList.push({
        value: rowData.name,
        subValue: rowData.code,
        valueColor: getValueColor(this.isDarkTheme),
        subValueColor: getSubValueColor(this.isDarkTheme),
        marketId: rowData.market,
      });

      // 表头对应的key列表，['涨幅', '规模', '持仓占比', '净值', '近1周', '近1月', '近3月', '近6月', '今年以来', '近1年']
      const riseValue = this.parseColorCell(rowData, riseSortIdKey(), fix2DicimalPercent, getZdfColor);
      // 规模
      const fundScale = this.parseNormalCell(rowData, 'fundScale', formatAmount, getValueColor);
      // 持仓占比
      const fundInvestRate = this.parseNormalCell(rowData, 'fundInvestRate', fix2DicimalPercent, getValueColor)
      // 净值
      const fix4Decimal = 4;
      const unitNav = {
        value: fixDecimals(rowData.unitNav, fix4Decimal),
        valueColor: getValueColor(this.isDarkTheme),
      };
      // 近1周
      const week = this.parseColorCell(rowData, 'week', fix2DicimalPercent, getZdfColor);
      // 近1月
      const month = this.parseColorCell(rowData, 'month', fix2DicimalPercent, getZdfColor);
      // 近3月
      const threeMonth = this.parseColorCell(rowData, 'tmonth', fix2DicimalPercent, getZdfColor);
      // 近6月
      const sixMonth = this.parseColorCell(rowData, 'hyear', fix2DicimalPercent, getZdfColor);
      // 今年以来
      const nowYear = this.parseColorCell(rowData, 'nowyear', fix2DicimalPercent, getZdfColor);
      // 近1年
      const year = this.parseColorCell(rowData, 'year', fix2DicimalPercent, getZdfColor );

      switch (getSecondSceneType(this.marketId, this.stockCode)) {
        case FuturesSceneSecondType.bondFutures:
          cellList.push(year, fundScale, unitNav, riseValue, week, month, threeMonth, sixMonth, nowYear)
          break;
        default:
          if (this.isBanKuai || getFirstSceneType(this.marketId) === SceneFirstType.hkPlate) {
            cellList.push(riseValue, fundScale, fundInvestRate, unitNav, week, month, threeMonth, sixMonth, nowYear, year)
          } else {
            cellList.push(riseValue, fundScale, unitNav, week, month, threeMonth, sixMonth, nowYear, year)
          }
      }

      return {
        index: rowIndex,
        stockCode: rowData.code,
        id: rowData.market,
        value: cellList,
      };
    },

    // 解析cell数据
    parseColorCell(rowData: any, keyToGet: string,
      formatValue: (value: string) => string,
      getColor: (value: string, isDarkTheme: boolean) => string): TableCellEntity {
      const valueStr = rowData[keyToGet];

      return {
        value: formatValue(valueStr),
        valueColor: getColor(valueStr, this.isDarkTheme),
      };
    },

    // 解析cell数据，数值不显示红绿色
    parseNormalCell(rowData: any, keyToGet: string,
      formatValue: (value: string) => string,
      getColor: (isDarkTheme: boolean) => string): TableCellEntity {
      const valueStr = rowData[keyToGet];

      return {
        value: formatValue(valueStr),
        valueColor: getColor(this.isDarkTheme),
      };
    },

    updatedTableData(tableData: TableData) {
      const codeListStr = tableData.list.map((item) => (item.stockCode + ':' + item.id)).join(',');
      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_show', EventTracingAction.show, {
        tabName: 'fund',
        fundCode: codeListStr,
        code: '',
        stock: this.stockCode + ':' + this.marketId,
        ...store.typeLogMap()
      });
    },

    // 处理列表点击事件
    handleItemClick(item: TableRowEntity, list: TableRowEntity[]): boolean {
      //iOS审核状态下不跳转
      if (!_t.judgeVersion()) {
        return false;
      }
      // 埋点
      sendClientCbas(
        ActionOperaType.JUMP,
        `${this.statModuleName}.gofund.${item.index}`,
        { toStockCode: item.stockCode },
      );

      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_fundcard', EventTracingAction.click, {
        fundCode: item.stockCode,
        marketId: item.id ?? "",
        stock: `${store.stockCode}:${store.marketId}`,
        ...store.typeLogMap()
      });

      this.gotoFund(item.stockCode);
      return true;
    },

    onHandleSort(sortMode: string, index: number) {
      // 点击表头排序，重置快捷排序tab状态
        sendEventCaptureRecord('ths_ifund_newfenshi_relatedfund_reOrder', EventTracingAction.click, {
        tabName: 'fund',
        stock: this.stockCode + ':' + this.marketId,
        position: index,
        ...store.typeLogMap()
        });
    },

    // 点击跳转场外基金
    gotoFund(fundCode: string) {
      HXJsBridge.jumpNativePage(
        `client.html?action=ijijin^action=fund,code=${fundCode}`
      );
    },
    onParseEmpty() {
      this.showEmpty = true;
      this.recommendOuterRequest();
    },
  },
});
</script>
