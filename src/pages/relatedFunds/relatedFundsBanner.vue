<template>
  <k-view class="related-fund-banner-root">
    <k-view class="banner-container" @click="openEtfDetailPage">
      <k-text class="text">不会选ETF？去ETF内容专区看看</k-text>
      <k-image class="arrow" :src="arrowIcon"></k-image>
    </k-view>
  </k-view>
</template>

<script>
import arrowRight from '../../assets/images/arrow_right.png';
import {sendClientCbas} from '@/lib/tools';
import { store } from '@/lib/store';
import {ActionOperaType} from '@/lib/constant';
import { sendEventCaptureRecord, EventTracingAction, jumpToNewWebPage } from '@/utils/HXTools';
const JUMP_URL = 'https://news.10jqka.com.cn/app/theme_front/theme/single/TZ-11683';

export default {
  props: {
    theme: {
      type: String,
      default: '',
      required: true,
    },
    statModuleName: String,
  },
  computed: {
    arrowIcon() {
      return arrowRight;
    },
  },
  mounted() {
    const cbas = `${this.statModuleName}.learn.etf.show`;
    sendClientCbas(ActionOperaType.SHOW, cbas);
  },
  methods: {
    // 跳转到ETF内容专区
    openEtfDetailPage() {
      const cbas = `${this.statModuleName}.learn.etf.more`;
      sendClientCbas(ActionOperaType.CLICK, cbas);
      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_djtj', EventTracingAction.click, {
        stock: `${store.stockCode}:${store.marketId}`,
        ...store.typeLogMap()
      });

      jumpToNewWebPage(JUMP_URL);
    },
  },
};
</script>

<style scoped>
.related-fund-banner-root {
  width: 100%;
  height: 56;
  padding-top: 12;
  flex-direction: row;
  justify-content: center;
}

.banner-container {
  width: 686px;
  height: 36;
  background-color: rgba(255, 102, 26, 0.1);
  border-radius: 4;
  flex-direction: row;
  align-items: center;
  padding: 0 8;
}

.text {
  font-size: 14;
  color: #ff661a;
  flex-grow: 1;
}

.arrow {
  width: 16;
  height: 16;
}
</style>
