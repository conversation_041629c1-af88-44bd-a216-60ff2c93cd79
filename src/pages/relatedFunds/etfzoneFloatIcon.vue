<template>
  <k-view :class="['etfzone-float-icon-root', theme]" @click="handleEtfzoneClick">
    <k-text :class="['text', theme]">ETF</k-text>
    <k-text :class="['text', theme]">专区</k-text>
  </k-view>
</template>

<script>
import errorIcon from '../../assets/images/data_error.png';
import errorIconDark from '../../assets/images/data_error_dark.png';
import { sendClientCbas } from '@/lib/tools';
import { ActionOperaType } from '@/lib/constant';
import { sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';
import { store } from '@/lib/store';

export default {
  name: 'hq-error-layout',
  props: {
    theme: {
      type: String,
      default: '',
      required: true,
    },
    statModuleName: String,
  },
  computed: {
    errorIcon() {
      return this.theme === 'dark' ? errorIconDark : errorIcon;
    },
  },
  methods: {
    handleEtfzoneClick() {
      const cbas = `${this.statModuleName}.etfzone`;
      sendClientCbas(ActionOperaType.CLICK, cbas);

      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_etfzhuanqu', EventTracingAction.click, {
        stock: `${store.stockCode}:${store.marketId}`,
        ...store.typeLogMap()
        });

      HXJsBridge.jumpNativePage(
        'client://client.html?action=ymtz^webid=2501'
      );
    },
  },
};
</script>

<style scoped>
.etfzone-float-icon-root {
  width: 88px;
  height: 88px;
  background-color: rgba(0, 0, 0, 0.7);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 44px;
}

.etfzone-float-icon-root.dark {
  background-color: rgba(255, 255, 255, 0.7);
}

.text {
  color: #ffffff;
  font-size: 12;
  line-height: 14;
}

.text.dark {
  color: #000000;
}
</style>
