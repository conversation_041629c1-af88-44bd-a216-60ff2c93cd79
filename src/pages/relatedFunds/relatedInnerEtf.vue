<template>
  <k-view class="related-etf-root">
    <RelatedFundsBanner :statModuleName="statModuleName" :theme="theme" />
    <view class="flex-d-r" v-if="quickTabShow">
      <tab
        ref="quciktab"
        class="flex-1"
        tab-type="solid"
        :theme="theme"
        :tabList="tabList"
        :selected-index="currentHeaderIndex"
        :style="{ height: 44 }"
        @onTabClick="onQuickSortHeaderClick"
      />
    </view>
    <HqTable
      ref="hqtable"
      v-if="!showEmpty"
      :request-type=1
      :headerFieldIds="headerFieldIds"
      :headerFieldNames="headerFieldNames"
      :sortIdProp="sortId"
      :sortOrderProp="sortOrder"
      :showPageInfo="true"
      :theme="theme"
      :onItemClick="handleItemClick"
      :height="tableHeight"
      @onHandleSort="onHandleSort"
      @onParseEmpty="onParseEmpty"
      @onParseError="onParseError"
      @updated-table-data="updatedTableData"
    >
    </HqTable>
    <RecommondFund v-else :dataList="recommendInnerList" moduleName="" hotText="大家都在关注" moreTitle="查看ETF热榜" type="inner"
      :pageId="pageId" :statModuleName="statModuleName"
      :emptyConfig="{ title: '暂无相关ETF', showMore: true, moreText: '去ETF专区，一站式选ETF' }">
    </RecommondFund>
    <EtfzoneFloatIcon class="etfzone-float-icon" :statModuleName="statModuleName" :theme="theme" />
    <GuideBubble
      v-if="fieldGuideShow"
      :content="fieldGuideContent"
      :position-style="fieldGuidePosition"
      button-text="下一步"
      arrorw-location="right"
      @bubble-click="fieldBubbleClick"
    />
    <GuideBubble
      v-if="quickTabGuideShow"
      :content="quickTabGuideContent"
      :position-style="quickTabGuidePosition"
      button-text="完成"
      arrorw-location="center"
      @bubble-click="quickTabGuideClick"
    />
  </k-view>
</template>

<script lang="ts">
import RelatedFundsBanner from './relatedFundsBanner.vue';
import Tab from '@/components/Tab/index.vue';
import HqTable from '@/components/hqTable/hqTable.vue';
import { TableData } from '@/components/hqTable/hqTable';
import { defineComponent } from '@king-fisher/app-vue';
import EtfzoneFloatIcon from './etfzoneFloatIcon.vue';
import { isPlate, sendClientCbas } from '@/lib/tools';
import { getMarketSortType } from '@/lib/common';
import { ActionOperaType, fieldConfig } from '@/lib/constant';
import { TableRowEntity } from '@/components/hqTable/hqTable';
import { jumpToFenShi, sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';
import RecommondFund from '@/modules/RecommondFund/index.vue';
import relatedFundMixin from '@/mixins/relatedFundMixin';
import dataUtilsMixin from '@/mixins/dataUtilsMixin';
import indexDataRequestMixin from '@/mixins/indexDataRequestMixin';
import GuideBubble from '@/components/Bubble/index.vue';
import Apis from '@king-fisher/apis';
import { isValidArray, isValidSrting } from '@/utils/commonTools.ts';
import { FuturesSceneSecondType, SceneFirstType } from '@/lib/SceneTypeDefines';
import { store } from '@/lib/store';

const GUIDE_SHOW_FLAG = 'guide_show_flag';
const QUICK_TAB_HEAT_INDEX = 2;
const QUICK_TAB_DEFAULT_INDEX = -1;
const QUICK_TAB_SORT_MODE = 'quickTab';

export default defineComponent({
  props: {
    stockCode: {
      type: String,
      required: true,
    },
    marketId: {
      type: String,
      required: true,
    },
    sortId: {
      type: String,
      required: true,
    },
    sortOrder: {
      type: String,
      required: true,
    },
    theme: {
      type: String,
      default: '',
    },
    relateViewHeight: {
      type: Number,
      required: true,
    },
    statModuleName: String,
    customTab: {
      type: Number
    },
    quickSortTabConfig: {
      type: Array
    }
  },
  mixins: [relatedFundMixin, dataUtilsMixin, indexDataRequestMixin],
  components: { RelatedFundsBanner, HqTable, EtfzoneFloatIcon, RecommondFund, GuideBubble, Tab },
  mounted() {
    // tab配置赋值
    this.tabList = this.quickSortTabConfig.map(item => item.title);
    // 是否自定义选中tab
    if (this.customTab > QUICK_TAB_DEFAULT_INDEX) {
      this.needCustomSelectField = true;
      this.onQuickSortHeaderClick(this.customTab);
      return;
    }
    // 是否有引导
    const needShowGuide = !isValidSrting(Apis.getStorageSync(GUIDE_SHOW_FLAG));
    if (needShowGuide && store.firstSceneType !== SceneFirstType.hkStock && store.firstSceneType !== SceneFirstType.usStock) {
      this.fieldGuideShow = true;
      this.needCustomSelectField = true;
      this.onQuickSortHeaderClick(QUICK_TAB_HEAT_INDEX);
    }
  },
  data() {
    return {
      tabList: [],
      statPrefix: '',
      // dp / pt 单位
      recommendInnerList: [],
      showEmpty: false,
      // 快速排序表头的下标
      currentHeaderIndex: QUICK_TAB_DEFAULT_INDEX,
      fieldGuideShow: false,
      quickTabGuideShow: false,
      fieldGuideContent: '新增2个字段辅助选择ETF，包括热度值和运作费率',
      quickTabGuideContent: '新增快捷排序功能帮助您选择ETF，点击快速尝试吧~',
      needCustomSelectField: false,
    };
  },
  computed: {
    isDarkTheme() {
      return this.theme === 'dark';
    },
    sortType() {
      return getMarketSortType(this.marketId);
    },
    isBanKuai() {
      return isPlate(this.marketId);
    },
    headerFieldIds() {
      if (store.firstSceneType === SceneFirstType.spot) {
        return fieldConfig.etfSpotFieldIds;
      }
      if (store.secondSceneType === FuturesSceneSecondType.bondFutures
       || store.secondSceneType === FuturesSceneSecondType.commodityFutures) {
        return fieldConfig.etfCommodityAndBondFuturesFieldIds;
      }
      if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
        return fieldConfig.hkAndUsStockFields;
      }
      if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        return fieldConfig.etfPlateFieldIds;
      }

      return fieldConfig.etfIndexFieldIds;
    },
    headerFieldNames() {
      if (store.firstSceneType === SceneFirstType.spot) {
        return fieldConfig.etfSpotFieldNames;
      }
      if (store.secondSceneType === FuturesSceneSecondType.bondFutures
       || store.secondSceneType === FuturesSceneSecondType.commodityFutures) {
        return fieldConfig.etfCommodityAndBondFuturesFieldNames;
      }
      if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
        return fieldConfig.hkAndUsStockFieldNames;
      }
      if (store.firstSceneType === SceneFirstType.hkPlate || this.isBanKuai) {
        return fieldConfig.etfPlateFieldNames;
      }
      return fieldConfig.etfIndexFieldNames;

    },
    quickTabShow() {
      return isValidArray(this.tabList) && store.firstSceneType !== SceneFirstType.hkStock && store.firstSceneType !== SceneFirstType.usStock;
    },
    tableHeight() {
      // 运营位banner高度
      const bannerHeight = 56;
      // 快捷排序tab高度
      const quickTabHeight = 44;

      let topHeight;
      if (this.quickTabShow) {
        topHeight = bannerHeight + quickTabHeight;
      } else {
        topHeight = bannerHeight;
      }

      return this.relateViewHeight - topHeight;
    },
    fieldGuidePosition() {
      return {
        height: 90,
        width: 210,
        top: 130,
        right: 16
      };
    },
    quickTabGuidePosition() {
      return {
        height: 90,
        width: 210,
        top: 95,
        left: 45
      };
    }
  },
  methods: {
    // 点击快捷排序表头
    onQuickSortHeaderClick(index: number) {
      if (index === this.currentHeaderIndex) {
        return;
      }
      sendEventCaptureRecord('ths_ifund_newfenshi_relatedfund_paixu', EventTracingAction.click, {
        tabName: 'ETF',
        stock: this.stockCode + ':' + this.marketId,
        position: index + 1,
        ...store.typeLogMap()
      });
      this.currentHeaderIndex = index;
      this.hqtableQuickTabSortAction();
    },
    fieldBubbleClick() {
      this.fieldGuideShow = false;
      this.quickTabGuideShow = true;
    },
    quickTabGuideClick() {
      this.quickTabGuideShow = false;
      Apis.setStorageSync(GUIDE_SHOW_FLAG, '1');
    },
    // 处理列表点击事件
    handleItemClick(item: TableRowEntity, list: TableRowEntity[]): boolean {
      // 埋点
      sendClientCbas(
        ActionOperaType.JUMP,
        `${this.statModuleName}.goetf.${item.index}`,
        { toStockCode: item.stockCode, toFrameId: '2205' },
      );

      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_etfcard', EventTracingAction.click, {
        code: item.stockCode,
        marketId: item.id,
        stock: store.stockCode + ':' + store.marketId,
        ...store.typeLogMap()
      });

      const codeList: string[] = [];
      const marketList: string[] = [];
      const nameList: string[] = [];
      list.forEach(e => {
        codeList.push(e.stockCode);
        marketList.push(e.id);
        nameList.push(e?.value[0]?.value);
      });
      jumpToFenShi(item.stockCode, item.id, { codeList, marketList, nameList });

      return true;
    },
    onHandleSort(sortMode: string, index: number) {
      // 点击表头排序，重置快捷排序tab状态
      if (sortMode !== QUICK_TAB_SORT_MODE) {
        sendEventCaptureRecord('ths_ifund_newfenshi_relatedfund_reOrder', EventTracingAction.click, {
        tabName: 'ETF',
        stock: this.stockCode + ':' + this.marketId,
        position: index,
        ...store.typeLogMap()
        });
        this.$refs.quciktab.resetSelectIndex();
        this.currentHeaderIndex = QUICK_TAB_DEFAULT_INDEX;
      }
    },
    onParseEmpty() {
      this.fieldGuideShow = false;
      this.quickTabGuideShow = false;

      this.showEmpty = true;
      this.recommendInnerRequest();
    },
    onParseError() {
      this.fieldGuideShow = false;
      this.quickTabGuideShow = false;
    },
    updatedTableData(tableData: TableData) {
      if (this.needCustomSelectField) {
        // 表格数据请求完成，首次选中自定义指标列
        this.hqtableQuickTabSortAction();
        this.needCustomSelectField = false;
        return;
      }
      const codeListStr = tableData.list.map((item) => (item.stockCode + ':' + item.id)).join(',');
      sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_show', EventTracingAction.show, {
        tabName: 'ETF',
        code: codeListStr,
        fundCode: '',
        stock: this.stockCode + ':' + this.marketId,
        ...store.typeLogMap()
      });
    },
    hqtableQuickTabSortAction() {
      const sortTabConfig = this.quickSortTabConfig[this.currentHeaderIndex];
      const sortIndex = this.headerFieldIds.indexOf(sortTabConfig.sortId);

      // 快捷排序规则排序
      this.$refs.hqtable.handleSort({
        sortId: sortTabConfig.sortId,
        sortOrder: sortTabConfig.sortOrder,
        sortMode: QUICK_TAB_SORT_MODE
      });
      // 滑动到指定列
      this.$refs.hqtable.changeColumn(sortIndex + 1);
    }
  },
});
</script>

<style scoped>
.related-etf-root {
  position: relative;
  width: 100%;
  height: 100%;
}

.etfzone-float-icon {
  position: absolute;
  right: 12;
  bottom: 140;
}

</style>
