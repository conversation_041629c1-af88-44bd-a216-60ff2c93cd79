<template>
  <view :class="['related-funds-root', theme]" ref="root">
    <TabBar
      :tabNames="tabNames"
      :index="curTabIndex"
      :onTabClick="handleTabClick"
      :statModuleName="statModuleName"
      :theme="theme"
    />

    <view v-if="relateViewHeight > 0">
      <RelatedInnerEtf v-if="curTabIndex === 0"
        :quickSortTabConfig="quickSortTabConfig"
        :stockCode="stockCode"
        :marketId="marketId"
        :customTab="customTab"
        :sortId="innerSortId"
        :sortOrder="innerSortOrder"
        :statModuleName="statModuleName"
        :relateViewHeight="relateViewHeight"
        :theme="theme"/>

      <RelatedOuterFund v-else
        :stockCode="stockCode"
        :market-id="marketId"
        :sortId="outerSortId"
        :sortOrder="outerSortOrder"
        :statModuleName="statModuleName"
        :relateViewHeight="relateViewHeight"
        :theme="theme"/>
    </view>
  </view>
</template>

<script>
import TabBar from './tabBar.vue';
import RelatedInnerEtf from './relatedInnerEtf.vue';
import RelatedOuterFund from './relatedOuterFund.vue';
import { store } from '@/lib/store';
import { isPlate } from '@/lib/tools';
import { HttpTitleFieldMap, TcpFieldMap } from '@/lib/constant';
import lifeTimeMixin from '@/mixins/lifeTimeMixin';
import { sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';
import { QuickSortTabIndexConfig, QuickSortTabPlateConfig } from '@/components/hqTable/hqConstant';
import { getFirstSceneType, getSecondSceneType } from '@/lib/SceneType';
import { SceneFirstType } from '@/lib/SceneTypeDefines';

// 参数中传递来的数值是字符串类型
const CHANNEL_OUTER_FUND = '2';

export default {
  mixins: [lifeTimeMixin],
  pageConfig: {
    canScroll: false,
  },
  components: {
    TabBar,
    RelatedInnerEtf,
    RelatedOuterFund
  },
  data() {
    return {
      tabCbasNames: ['ths_mob_newfenshi_relatedfund_ETF', 'ths_mob_newfenshi_relatedfund_fund'],
      // 当前选中 tab index
      curTabIndex: 0,
      stockCode: '',
      stockName: '',
      marketId: '',
      channelType: '',
      customTab: -1,
      innerSortOrder: '0',
      outerSortOrder: '0',
      // 埋点页面名称
      statModuleName: 'fenshi_jijintab_all',
      theme: '',
      // 标题栏以下，导航栏以上内容区域高度。Table组件的height属性只能设置一次，所以在未获取到组件高度之前，先设置高度为0,并隐藏组件。
      contentHeight: 0,
    };
  },
  computed: {
    // 场外基金默认排序字段，板块使用持仓占比，指数使用涨幅
    outerSortId() {
      if (isPlate(this.marketId) || store.firstSceneType === SceneFirstType.hkPlate) {
        return '36152';
      } else {
        return '36164';
      }
    },
    // 场内ETF默认排序字段，板块使用持仓占比，指数使用涨幅
    innerSortId() {
      if (store.firstSceneType === SceneFirstType.hkStock
        || store.firstSceneType === SceneFirstType.usStock
        || store.firstSceneType === SceneFirstType.hkPlate
        || isPlate(this.marketId)) {
        return HttpTitleFieldMap.investRate;
      } else {
        return TcpFieldMap.rise;
      }
    },
    relateViewHeight() {
      if (this.contentHeight === 0) {
        return 0;
      }

      const tabHeight = 40;
      const quickSortTabHeight = 44;
      return this.contentHeight - tabHeight - quickSortTabHeight;
    },
    quickSortTabConfig() {
      if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
        return [];
      } else {
        return isPlate(this.marketId) ? QuickSortTabPlateConfig : QuickSortTabIndexConfig;
      }
    },
    tabNames() {
      if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
        return ['相关ETF'];
      } else {
        return ['相关ETF', '场外基金'];
      }
    },
  },
  created() {
    if (_t.isGrayReleaseOn('HQ_FenshiKlineSameScreen')) {
      this.statModuleName = 'newfenshi_fenshi_jijintab_all';
    }

    this.loadEventListener();

    if (Hummer?.pageInfo?.params) {
      const { stockCode, marketId, stockName, channelType, sortId, sortOrder } = Hummer.pageInfo.params;
      this.marketId = marketId || this.marketId;
      this.stockCode = stockCode || this.stockCode;
      this.stockName = stockName || this.stockName;
      this.channelType = String(channelType) || this.channelType;
      this.curTabIndex = this.channelType === CHANNEL_OUTER_FUND ? 1 : 0;

      this.customTab = this.quickSortTabConfig.findIndex(config => {
        return config.sortId === `${sortId}` && config.sortOrder === `${sortOrder}`;
      });

      store.marketId = this.marketId;
      store.stockCode = this.stockCode;
      store.firstSceneType = getFirstSceneType(this.marketId)
      store.secondSceneType = getSecondSceneType(this.marketId, this.stockCode)
    }
  },
  mounted() {
    this.theme = store.theme;
    this.$nextTick(() => {
      if (this.$refs.root) {
        this.$refs.root.getRect(rect => {
          this.contentHeight = rect.height;
        });
      }
    });
  },
  methods: {

    handleTabClick(index) {
      this.curTabIndex = index;
      if (index >= 0 && index < this.tabCbasNames.length) {
        sendEventCaptureRecord(this.tabCbasNames[index], EventTracingAction.click, {
          stock: `${store.stockCode}:${store.marketId}`,
          ...store.typeLogMap()
        });
      }
    },

    didChangedTheme(theme) {
      this.theme = theme;
    },
  },
};
</script>
<style lang="less">

.related-funds-root {
  width:100%;
  height:100%;
  background-color: #ffffff;
}

.related-funds-root.dark {
  background-color: #1c1c1c;
}
</style>
