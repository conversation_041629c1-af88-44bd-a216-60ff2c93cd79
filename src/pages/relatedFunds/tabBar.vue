<template>
  <k-view class="tab-bar-root">
    <tab-item
      class="tab-item"
      v-for="(name, index) in tabNames"
      :key="index"
      :theme="theme"
      :name="name"
      :isSelected="index == curIndex"
      @click="handleTabClick(index)"/>
  </k-view>
</template>

<script>
import tabItem from './tabItem.vue';
import {sendClientCbas} from '@/lib/tools';
import {ActionOperaType} from '@/lib/constant';

export default {
  components: { tabItem },
  props: {
    tabNames: {
      type: [],
      default: [],
      required: true,
    },
    index: {
      type: Number,
      default: 0,
    },
    // 通知外部tab选中结果
    onTabClick: Function,
    theme: {
      type: String,
      default: '',
      required: true,
    },
    statModuleName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      curIndex: 0,
    };
  },
  watch: {
    index: {
      handler(value) {
        this.curIndex = value;
      },
      immediate: true,
    },
  },
  methods: {
    handleTabClick(index) {
      if (index === 0) {
        const cbas = `${this.statModuleName}.etf`;
        sendClientCbas(ActionOperaType.CLICK, cbas);
      } else {
        const cbas = `${this.statModuleName}.fund`;
        sendClientCbas(ActionOperaType.CLICK, cbas);
      }
      this.curIndex = index;
      this.onTabClick(index);
    },
  },
};
</script>

<style scoped>
.tab-bar-root {
  width: 100%;
  height: 40;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.tab-item {
  width: 50%;
  height: 100%;
}
</style>
