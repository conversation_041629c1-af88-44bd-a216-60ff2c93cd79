<template>
  <k-view class="tab-item-root">
    <k-text :class="['tab-name', selected, theme]">{{ name }}</k-text>
    <k-view :class="['index', selected]"></k-view>
  </k-view>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: '--',
      required: true,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: '',
      required: true,
    },
  },
  computed: {
    selected() {
      return this.isSelected ? 'selected' : '';
    },
  },
};
</script>

<style scoped>
.tab-item-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.tab-name {
  font-size: 16;
  color: rgba(0, 0, 0, 0.6);
  font-weight: normal;
}

.tab-name.selected {
  color: rgba(0, 0, 0, 0.84);
  font-weight: bold;
}

.tab-name.dark {
  color: rgba(255, 255, 255, 0.6);
}

.tab-name.selected.dark {
  color: rgba(255, 255, 255, 0.84);
}

.index {
  width: 12;
  height: 3;
  margin-top: 4;
  margin-bottom: 3;
  border-radius: 1;
  background-color: rgba(255, 255, 255, 0);
}

.index.selected {
  background-color: #FF2436;
}
</style>
