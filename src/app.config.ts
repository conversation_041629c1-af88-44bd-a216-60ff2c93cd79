export default defineAppConfig({
  routes: [
    /** pages */
    //指数板块分时相关基金tab
    {
      path: '/pages/index',
      name: 'index',
      source: './pages/index/app.vue',
      beforeRender: './pages/index/beforeRender.js',
    },
    //指数板块分时相关基金tab二级页
    {
      path: '/pages/relatedFunds',
      name: 'relatedFunds',
      source: './pages/relatedFunds/index.vue',
    },
  ],
  spaConfig: {
    documentPath: '../public/index.html',
    window: {
      title: 'kingfisher-vue-demo',
    },
    beforeRender: './beforeRender.js',
    scripts: [
      // skywalking
      `<script src="https://s.thsi.cn/hxapp/m/base/js/skywalking.1.1.5.min.js"></script>`,
      // 手炒埋点
      `<script src="https://s.thsi.cn/cb?cd/ths-frontend-common-lib-container/v1.3.3/;common/bridge.js;business/stat.js"></script>`,
    ],
  },
});
