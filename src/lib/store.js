import { reactive } from '@king-fisher/tenon-vue';
import { getSecondSceneType, getFirstSceneType } from './SceneType';

export const store = reactive({
  theme: '',
  blind: '',
  stockCode: '',
  marketId: '',
  stockName: '',
  firstSceneType:'',
  secondSceneType:'',
  viewDidShowEventState: 0,
  notifiViewDidShowEvent() {
    this.viewDidShowEventState++;
  },
  changeTheme(theme) {
    this.theme = theme;
  },
  changeBlind(theme) {
    this.blind = theme;
  },
  typeLogMap() {
    const { marketId, stockCode } = Hummer.pageInfo.params;
      return {
        "type": getFirstSceneType(marketId),
        "logType": getSecondSceneType(marketId, stockCode)
      }
  }
});
