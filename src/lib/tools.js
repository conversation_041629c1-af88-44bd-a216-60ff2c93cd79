import { HqColor, colorHeaders, fixDecimals } from '@/components/hqTable/hqTool';
import { isValidSrting } from '@/utils/commonTools';

const DATE_LENGTH = 8;
const Hexadecimal = 16;
const Hexadecimal_Num3 = 0x3;
const Hexadecimal_Num8 = 0x8;

// 获取当前ios安卓
function getPlatform() {
  return Hummer.env.platform === 'iOS' ? 'ios' : 'android';
}

// 获取所有cookie
export function getCookie() {
  return HXJsBridge.getUserCookie();
}
// 获取服务器时间
function getServerTime() {
  return HXJsBridge.getServerTime();
}
// 获取交易日相关信息
function getTradingDay() {
  return HXJsBridge.tradingDay();
}
// 判断是否开户 返回 0 || 1
function isBindTradeAccount() {
  return HXJsBridge.isBindTradeAccount();
}
// 判断行情连接是否在线
function checkHqIsOnline() {
  return HXJsBridge.isHqConnected();
}

function isGrayReleaseOn(key) {
  return HXJsBridge.isGrayReleaseOn(key);
}

// 判断是否为数字
function isNumber(obj) {
  return typeof obj === 'number' && !isNaN(obj);
}

function statusBarAndNavigationHeight() {
  const androidStatusBarHeight = 42;
  const iOSStatusBarHeight = 44;
  if (Hummer.env.platform !== 'iOS') {
    return androidStatusBarHeight;
  }
  return iOSStatusBarHeight + Number(Hummer.env.statusBarHeight);
}

// 保存最后一个埋点，避免相同埋点重复发送
let lastCbasMsg = '';

/**
 * 埋点
 *  @param {number} type 埋点类型 0展示 1点击 2跳转
 *  @param {string} object 需发送埋点
 *  @param {string} toStockCode 目的页面的股票code
 *  @param {string} toFrameId 目的页面的页面id
 *  @param {string} targid 目标地址的pageId
 *  @param {string} flag 新增字段
 *  @param {string} toResourcesId 网页的统计id，可以是网页的seq，产品id等
 */
export function sendClientCbas(
  type,
  object,
  { toStockCode = '', toFrameId = '', targid = '', flag = '', toResourcesId = '' } = {}
) {
  const msg = `${type}.${object}.${toStockCode}.${toFrameId}.${targid}.${flag}.${toResourcesId}`;
  if (msg === lastCbasMsg) {
    return;
  }
  lastCbasMsg = msg;

  const params = {
    //埋点类型 0展示 1点击 2跳转
    type,
    // 业务定
    object,
    to_resourcesid: toResourcesId,
    from_resourcesid: '',
    to_frameid: toFrameId || '',
    to_scode: toStockCode || '',
    targid: targid || '',
    //  当type为1时，action_type才起效
    action_type: '',
    flg: flag,
  };
  HXJsBridge.sendClientCbas(params);
}

// 埋点
export function sendHomePageCbas(
  type,
  object,
  { targid = '', flag = '', toResourcesId = '', toStockCode = '' } = {}
) {
  const newObject = `hangqing_etf.${object}`;
  sendClientCbas(type, newObject, { targid, flag, toResourcesId, toStockCode });
}

// 封装error
export class HxError extends Error {
  constructor(name, message) {
    super();
    this.name = name;
    this.message = typeof message === 'string' ? message : JSON.stringify(message);
  }
}

// 封装 JSON.parse() 若有错误抛出位置
function jsonParseThrow(params, place) {
  try {
    return JSON.parse(params);
  } catch (err) {
    throw new HxError('JSON.parse error', JSON.stringify(place, params));
  }
}

// 格式化日期 yyyymmdd => yyyy-mm-dd
export function formatDate(yyyymmdd) {
  const value = parseInt(yyyymmdd, 10);
  if (!!yyyymmdd && yyyymmdd.length === DATE_LENGTH && !isNaN(value)) {
    return value.toString().replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
  } else {
    return '--';
  }
}

export function isStartWithPureCharater(originStr, prefix) {
  const reg = new RegExp(`^${prefix}[0-9]`);
  return reg.test(originStr);
}

// 是否有效 stockCode
export function isValidCode(code) {
  return !!code && '--' !== code;
}

export function getHummerValueColor(isDarkTheme) {
  return isDarkTheme ? HqColor.valueColor.dark : HqColor.valueColor.light;
}

export function getHummerSubValueColor(isDarkTheme) {
  return isDarkTheme ? HqColor.subValueColor.dark : HqColor.subValueColor.light;
}

// 小数点位数
const float4Fix = 4;

// 获取类似涨跌幅一样带红绿色的字段字体色
export function getHummerZdfColor(value, isDarkTheme, isBlind) {
  if (fixDecimals(value, float4Fix) < 0) {
    // 色盲模式
    if (isBlind) {
      return isDarkTheme ? HqColor.blind.dark : HqColor.blind.light;
    }
    return isDarkTheme ? HqColor.greenColor.dark : HqColor.greenColor.light;
  }
  if (fixDecimals(value, float4Fix) > 0) {
    return isDarkTheme ? HqColor.redColor.dark : HqColor.redColor.light;
  }
  return getHummerValueColor(isDarkTheme);
}

// 格式化处理列表数据项颜色
export function getHummerCellColor(value, valueType, isDarkTheme) {
  if (colorHeaders.has(valueType)) {
    return getHummerZdfColor(value, isDarkTheme);
  }
  return getHummerValueColor(isDarkTheme);
}

//是否受限版本，仅供iOS提审使用, true表示不受限，false表示受限
export function judgeVersion() {
  if (__GLOBAL__.isIos && HXJsBridge.judgeVersion) {
    return HXJsBridge.judgeVersion();
  }
  return true;
}

// 向url链接拼接参数
export function appendUrlParam(url, key, value) {
  if (!url || !key) {
    // url 或 key 不能为空
    return url;
  }

  return url.indexOf('?') === -1 ? `${url}?${key}=${value}` : `${url}&${key}=${value}`;
}

// 向url链接拼接一组参数
export function appendUrlParams(url, keys, values) {
  let webUrl = url;

  if (keys?.length === values?.length) {
    for (let i = 0; i < keys.length; i++) {
      webUrl = appendUrlParam(webUrl, keys[i], values[i]);
    }
  }

  return webUrl;
}

// 判断 marketId 是否板块
export function isPlate(marketId) {
  if (!marketId) {
    return false;
  }
  const market = String(marketId);
  return market === '48' || market === '49';
}

export function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c){
    const r = Math.random() * Hexadecimal | 0;
    const v = c === 'x' ? r : ( r & Hexadecimal_Num3 | Hexadecimal_Num8);
    return v.toString(Hexadecimal);
  });
}

export default {
  getPlatform,
  sendClientCbas,
  sendHomePageCbas,
  isNumber,
  getServerTime,
  getTradingDay,
  isBindTradeAccount,
  checkHqIsOnline,
  isGrayReleaseOn,
  HxError,
  jsonParseThrow,
  statusBarAndNavigationHeight,
  judgeVersion,
};
