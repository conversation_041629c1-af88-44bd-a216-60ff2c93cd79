import { sceneFirstTypeMap, SceneFirstType, IndexPlateSceneSecondType, HKPlateSceneSecondType, FuturesSceneSecondType, SpotSceneSecondType } from '@/lib/SceneTypeDefines';
import { isStartWithPureCharater } from '@/lib/tools';

/**
 * 获取一级场景分类
 * @param marketId 
 * @returns 
 */
export function getFirstSceneType(marketId: string): string {
  return getSpotFirstType(marketId) 
  ?? sceneFirstTypeMap[marketId] 
  ?? SceneFirstType.unknown
}

export function getSecondSceneType(marketId: String, stockCode: string): string {
  return getIndexPlateSecondType(marketId, stockCode)
  ?? getFuturesSecondType(marketId, stockCode)
  ?? getSpotSecondType(marketId, stockCode)
  ?? getHKPlateSecondType(marketId, stockCode)
  ?? ""
}

/**
 * 获取指数板块二级场景分类
 * @param marketId marketId
 * @param stockCode stockCode
 * @returns 
 */
export function getIndexPlateSecondType(marketId: String, stockCode: string): string | null {
  if (marketId === "88"
     || marketId == "176"
     || marketId == "216"
     || marketId == "UNSI") {
    return IndexPlateSceneSecondType.notAIndex
  }

  if (marketId == "16"
     || marketId == "32"
     || marketId == "109"
     || marketId == "110"
     || marketId == "120"
     || marketId == "UMCI"
     || marketId == "UZXI") {
    return IndexPlateSceneSecondType.aIndex
  }

  if (marketId == "48") {
    if (stockCode.startsWith("881")) {
      return IndexPlateSceneSecondType.aPlateIndustry
    }

    if (stockCode.startsWith("882")) {
      return IndexPlateSceneSecondType.aPlateRegion
    }
    if (stockCode.startsWith("883")) {
      return IndexPlateSceneSecondType.aPlateStyle
    }
    if (stockCode.startsWith("885")
       || stockCode.startsWith("886")
       || stockCode.startsWith("887")) {
      return IndexPlateSceneSecondType.aPlateConcept
    }
  }
  return null
}

/**
 * 获取港股板块二级分类
 * @param marketId marketId
 * @param stockCode stockCode
 * @returns 
 */
export function getHKPlateSecondType(marketId: String, stockCode: string): string | null {
  if (marketId === "90" && stockCode.startsWith("871")) {
    return HKPlateSceneSecondType.hkPlateIndustry
  }
  if (marketId === "90" && stockCode.startsWith("875")) {
    return HKPlateSceneSecondType.hkPlateConcept
  }
  return null
}

/**
 * 获取二级期货分类
 * @param marketId marketId
 * @param stockCode stockCode
 * @returns 
 */
export function getFuturesSecondType(marketId: String, stockCode: string): string | null {
  if (marketId === "65"
    || marketId === "66"
    || marketId === "67"
    || marketId === "UGFF"
    || (marketId === "UCXF" && (isStartWithPureCharater(stockCode, "GC") || isStartWithPureCharater(stockCode, "SI")))) {
    return FuturesSceneSecondType.commodityFutures
  }

  if (marketId === "129" && (isStartWithPureCharater(stockCode, "T")
    || isStartWithPureCharater(stockCode, "TL")
    || isStartWithPureCharater(stockCode, "TS")
    || isStartWithPureCharater(stockCode, "TF"))) {
      return FuturesSceneSecondType.bondFutures
  }

  if (marketId === "129"
    || marketId === "217"
    || marketId === "UCTF") {
      return FuturesSceneSecondType.indexFutures
  }

  return null
}


export function getSpotFirstType(marketId: String): string | null {
  const { stockCode } = Hummer.pageInfo.params;

  const secondType = getSpotSecondType(marketId, stockCode);
  if (secondType) {
    return SceneFirstType.spot;
  }

  return null;
}



export function getSpotSecondType(marketId: String, stockCode: string): string | null {
  if (marketId === "81") {
    return SpotSceneSecondType.inSpot;
  }

  if (marketId === "218"
    || (marketId === "97" && stockCode === "XAUUSD")) {
    return SpotSceneSecondType.outSpot;
  }

  return null;
}