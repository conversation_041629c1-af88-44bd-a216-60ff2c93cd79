/// 存放多组件共用业务逻辑
import { isPlate } from '@/lib/tools';
import { getFirstSceneType } from '@/lib/SceneType'
import { SceneFirstType } from '@/lib/SceneTypeDefines';

// 获取场外基金列表请求链接
export function getOuterFundUrl(marketId: string) {
  switch (getFirstSceneType(marketId)) {
    case SceneFirstType.spot:
      return __GLOBAL__.url.spotOutFund;
    case SceneFirstType.futures:
      return __GLOBAL__.url.futuresOutFund;
    case SceneFirstType.hkPlate:
      return __GLOBAL__.url.hkPlateOutFund;
    default:
      return getAStockOutFundUrl(marketId);
  }
}

function getAStockOutFundUrl(marketId: String) {
  if (isPlate(marketId)) {
    return __GLOBAL__.url.PlateOutFund;
  } else {
    return __GLOBAL__.url.indexOutFund;
  }
}

// 获取行情请求 sortType
export function getMarketSortType(marketId: string) {
  return isPlate(marketId) ? 'sector_3_0' : 'zzgz';
}

// 断开unified通道连接
export function disConnectHQ(requestBridge: any) {
  try {
    requestBridge.onDisappear();
  } catch (e) {
    // 错误处理
  }
}
