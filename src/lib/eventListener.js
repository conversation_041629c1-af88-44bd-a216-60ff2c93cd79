/*
 * @Author: your name
 * @Date: 2022-03-26 17:18:21
 * @LastEditTime: 2022-03-26 17:31:27
 * @LastEditors: Please set LastEditors
 * @Description: NotifyCenterProxy.removeEventListener 安卓存在无法清楚的问题，故重写为如下
 * @FilePath: \myConcernd:\项目\ths-homepage-newUser\homepage\src\lib\eventListener.js
 */
const androidNotify = {
  /**
   * 设置消息监听事件
   *
   * @param event 事件名称
   * @param callback 接收消息回调，value为消息内容
   */
  addEventListener(event, callback) {
    Hummer.notifyCenter.addEventListener(event, callback);
  },
  /**
   * 取消消息监听事件
   *
   * @param event 事件名称
   * @param callback 接收消息回调，addEventListener时的callback对象
   */
  removeEventListener(event) {
    Hummer.notifyCenter.removeEventListener(event);
  },
  /**
   * 发送消息
   *
   * @param event 事件名称
   * @param value 消息内容
   */
  triggerEvent(event, value) {
    Hummer.notifyCenter.triggerEvent(event, value);
  },
};
const eventListener = Hummer.env.platform === 'iOS' ? Hummer.notifyCenter : androidNotify;
export default eventListener;
