export enum SceneFirstType {
  unknown = "",
  /// 指数板块
  indexPlate = "zsbk",
  /// A股个股
  aStock = "aggg",
  /// 港股个股
  hkStock = "gggg",
  /// 港股板块
  hkPlate = "ggbk",
  /// 美股个股
  usStock = "mggg",
  /// 美股板块
  usPlate = "mgbk",
  /// 期货
  futures = "qh",
  // 现货
  spot = 'xh'
}

export enum IndexPlateSceneSecondType {
  unknown = "",
  /// A股指数
  aIndex = "agzs",
  /// 非A股的指数
  notAIndex = "fagzs",
  /// A股板块行业
  aPlateIndustry = "agbkhy",
  /// A股板块概念
  aPlateConcept = "agbkgn",
  /// A股板块地域
  aPlateRegion = "agbkdy",
  /// A股板块风格
  aPlateStyle = "agbkfg"
}

export enum HKPlateSceneSecondType {
  unknown = "",
  /// 港股板块行业
  hkPlateIndustry = "ggbkhy",
  /// 港股板块概念
  hkPlateConcept = "ggbkgn",
}

export enum FuturesSceneSecondType {
  unknown = "",
  /// 股指类期货
  indexFutures = "gzqh",
  /// 商品类期货
  commodityFutures = "spqh",
  /// 债券类期货
  bondFutures = "zqqh",
}


export enum SpotSceneSecondType {
  unknown = "",
  /// 内盘现货
  inSpot = "npxh",
  /// 外盘现货
  outSpot = "wpxh",
}


// 定义类型
export type SceneFirstTypeMap = {
  [key: string]: SceneFirstType;
};

export const sceneFirstTypeMap: SceneFirstTypeMap = {
  /// 指数板块
  "16": SceneFirstType.indexPlate,
  "32": SceneFirstType.indexPlate,
  "48": SceneFirstType.indexPlate,
  "49": SceneFirstType.indexPlate,
  "88": SceneFirstType.indexPlate,
  "109": SceneFirstType.indexPlate,
  "110": SceneFirstType.indexPlate,
  "120": SceneFirstType.indexPlate,
  "216": SceneFirstType.indexPlate,
  "176": SceneFirstType.indexPlate,
  "UNSI": SceneFirstType.indexPlate,
  "UMCI": SceneFirstType.indexPlate,
  "UZXI": SceneFirstType.indexPlate,
  /// A股个股
  "17": SceneFirstType.aStock,
  "33": SceneFirstType.aStock,
  /// 港股个股
  "177": SceneFirstType.hkStock,
  "178": SceneFirstType.hkStock,
  /// 港股板块
  "90": SceneFirstType.hkPlate,
  /// 美股个股
  "169": SceneFirstType.usStock,
  "170": SceneFirstType.usStock,
  "185": SceneFirstType.usStock,
  "186": SceneFirstType.usStock,
  /// 美股板块
  "89": SceneFirstType.usPlate,
  /// 期货
  "65": SceneFirstType.futures,
  "66": SceneFirstType.futures,
  "67": SceneFirstType.futures,
  "129": SceneFirstType.futures,
  "217": SceneFirstType.futures,
  "UGFF": SceneFirstType.futures,
  "UCXF": SceneFirstType.futures,
  "UCTF": SceneFirstType.futures,
  /// 现货,97只有XAUUSD是现货，其他不是，这里未列出，获取的时候会通过方法进行判断
  "81": SceneFirstType.spot,
  "218": SceneFirstType.spot,
};