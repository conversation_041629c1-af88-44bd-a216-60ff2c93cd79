import { fieldIds } from '@/components/hqTable/hqConstant';
import { store } from '@/lib/store';
import { getFirstSceneType, getSecondSceneType } from '@/lib/SceneType'

// 封装热点主题对象
export class ETFModel {
  name = '--';
  code = '';
  marketId = '';
  //板块涨跌幅
  rise = { value: '--', valueColor: '', darkValueColor: '' };
  similarity = '';
  positionRatio = '';
  scale = '';
  // 成交额
  tradingVolume = '';
  // 净值
  unitNav = '';
  // 溢价率
  premiumRate = '';
}

export class RecommendModel {
  name = '--';
  code = '';
  marketId = '';
  //板块涨跌幅
  rise = { value: '--', valueColor: '', darkValueColor: '' };
  tag = '';
  description = '';
  duration = '--';
}

export const ActionOperaType = {
  // 页面显示
  SHOW: 0,
  // 点击
  CLICK: 1,
  // 跳转
  JUMP: 2,
  // 其他
  OTHER: 3,
};

export const PageLevel = {
  level1: '1',
  level2: '2'
};

// 表格标题列字段id
// HXTable要求传入number值，自定义
export const HttpTitleFieldMap = {
  heat: '1001',
  operFee: '1002',
  scale: '1003',
  similar: '1004',
  investRate: '1005',
};

// http接口数据字段key
export const HttpDataFieldMap = {
  heat: 'HEAT',
  operFee: 'OPER_FEE',
  scale: 'FUND_SCALE',
  similar: 'FUND_PERFORMANCE_SIMILARITY',
  investRate: 'FUND_INVEST_RATE',
};

// tcp接口数据字段key
export const TcpFieldMap = {
  name: '55',
  code: '4',
  rise: '33001',
  tradeVol: '19',
  premiumRate: '33004',
  latestPrice: '10',
  week: '34376',
  month: '3252',
  threeMonth: '34850',
  halfYear: '34851',
  year: '34852'
};

export const TitleFieldDataKeyMap = {
  '1001': 'heat',
  '1002': 'operFee',
  '1003': 'scale',
  '1004': 'similar',
  '1005': 'investRate',
  '33001': 'rise',
  '19': 'tradeVol',
  '33004': 'premiumRate',
  '10': 'latest',
  '34376': 'week',
  '3252': 'month',
  '34850': 'tmonth',
  '34851': 'hyear',
  '34852': 'year',
};

const commonFieldIds = [
  TcpFieldMap.tradeVol,
  TcpFieldMap.premiumRate,
  HttpTitleFieldMap.heat,
  HttpTitleFieldMap.scale,
  HttpTitleFieldMap.operFee,
  TcpFieldMap.latestPrice,
  TcpFieldMap.week,
  TcpFieldMap.month,
  TcpFieldMap.threeMonth,
  TcpFieldMap.halfYear,
  TcpFieldMap.year
];

const etfPlateFieldIds = [
  TcpFieldMap.rise,
  HttpTitleFieldMap.similar,
  HttpTitleFieldMap.investRate,
  ...commonFieldIds
];

const etfIndexFieldIds = [
  TcpFieldMap.rise,
  ...commonFieldIds
];

const hkAndUsStockFields = [
  TcpFieldMap.rise,
  HttpTitleFieldMap.investRate,
  ...commonFieldIds
];

const etfCommodityAndBondFuturesFieldIds = [
  TcpFieldMap.rise,
  TcpFieldMap.premiumRate,
  TcpFieldMap.tradeVol,
  TcpFieldMap.latestPrice,
  HttpTitleFieldMap.scale,
  HttpTitleFieldMap.heat,
  HttpTitleFieldMap.operFee,
  TcpFieldMap.week,
  TcpFieldMap.month,
  TcpFieldMap.threeMonth,
  TcpFieldMap.halfYear,
  TcpFieldMap.year
];

// 表头字段
export const fieldConfig = {
  // ETF板块 fieldIds
  etfPlateFieldIds,
  // ETF指数 fieldIds
  etfIndexFieldIds,
  // ETF商品、债券类期货 fieldIds
  etfCommodityAndBondFuturesFieldIds,
  // ETF板块 fieldNames
  etfPlateFieldNames: ['涨幅', '业绩相似度', '持仓占比', '成交额', '溢价率', '热度', '规模', '运作费率', '最新', '近1周', '近1月', '近3月', '近6月', '近1年'],
  // ETF指数 fieldNames
  etfIndexFieldNames: ['涨幅', '成交额', '溢价率', '热度', '规模', '运作费率', '最新', '近1周', '近1月', '近3月', '近6月', '近1年'],
  // ETF商品、债券类期货 fieldNames
  etfCommodityAndBondFuturesFieldNames: ['涨幅', '溢价率', '成交额', '最新', '规模', '热度', '运作费率', '近1周', '近1月', '近3月', '近6月', '近1年'],
  // 场外基金股指期货 fieldIds
  fundBondFuturesFieldIds: ['36162', '36157', '36156', '36164', '36158', '36159', '36160', '36161', '36163'],
  // 场外基金债券类期货 fieldNames
  fundBondFuturesFieldNames: ['近1年', '规模', '净值', '涨幅', '近1周', '近1月', '近3月', '近6月', '今年以来'],
  // 场外基金板块 fieldIds
  fundPlateFieldIds: ['36164', '36157', '36152', '36156', '36158', '36159', '36160', '36161', '36163', '36162'],
  // 场外基金指数 fieldIds
  fundIndexFieldIds: ['36164', '36157', '36156', '36158', '36159', '36160', '36161', '36163', '36162'],
  // 场外基金板块 fieldNames
  fundPlateFieldNames: ['涨幅', '规模', '持仓占比', '净值', '近1周', '近1月', '近3月', '近6月', '今年以来', '近1年'],
  // 场外基金指数 fieldNames
  fundIndexFieldNames: ['涨幅', '规模', '净值', '近1周', '近1月', '近3月', '近6月', '今年以来', '近1年'],

  hkAndUsStockFields,
  hkAndUsStockFieldNames: ['涨幅', '持仓占比', '成交额', '溢价率', '热度', '规模', '运作费率', '最新', '近1周', '近1月', '近3月', '近6月', '近1年'],

  // ETF 现货
  etfSpotFieldIds: ['33001', '33004', '19', '10', '1003', '1001', '1002', '34376', '3252', '34850', '34851', '34852'],
  etfSpotFieldNames:  ['涨幅', '溢价率', '成交额', '最新', '规模', '热度', '运作费率', '近1周', '近1月', '近3月', '近6月', '近1年'],
};

/**
 * 用于基金tab问财运营位获取大模型推荐内容的场景标识
 */
export const FundTabAIMEScene = 'ths_index_fund_query';

/**
 * 一级页指标配置
 */
export const headerSimilarity = {
  key: 'similarity',
  name: '业绩相似度',
  showHelp: true
};
export const headerPosition = {
  key: 'positionRatio',
  name: '持仓占比',
  showHelp: true
};
export const headerVolume = {
  key: 'tradingVolume',
  name: '成交额',
  showHelp: false
};
export const headerScale = {
  key: 'scale',
  name: '规模',
  showHelp: false
};
export const headerUnit = {
  key: 'unitNav',
  name: '净值',
  showHelp: false
};
export const premiumRate = {
  key: 'premiumRate',
  name: '溢价率',
  showHelp: false
};
