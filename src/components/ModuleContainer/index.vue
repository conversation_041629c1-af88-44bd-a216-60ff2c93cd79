<template>
  <k-view :class="['module-container', theme]" :style="{ width: containerWidth }">
    <k-view class="flex header">
      <k-view class="flex title-container">
        <k-text :class="['title', theme]">{{ title }}</k-text>
      </k-view>
      <k-view v-if="showMore" class="flex ai-c" @click="onMoreClick">
        <k-text :class="['more', theme]">{{ moreText }}</k-text>
        <k-image :src="moreIconSrc" class="more-icon"></k-image>
      </k-view>
    </k-view>
    <slot></slot>
  </k-view>
</template>

<script>
import lifeTimeMixin from '@/mixins/lifeTimeMixin';
import moreIcon from '@/assets/images/container_more.png';
import moreIconDark from '@/assets/images/container_more_dark.png';
import { store } from '@/lib/store';

const CONTAINERMARGIN = 12;
export default {
  name: 'ModuleContainer',
  mixins: [lifeTimeMixin],
  props: {
    title: {
      type: String,
      default: '',
    },
    moreText: {
      type: String,
      default: '',
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    moduleName: {
      type: String,
      default: 'unknown',
    },
  },
  data() {
    return {
      containerWidth: __GLOBAL__.screenWidth - CONTAINERMARGIN,
      theme: '',
    };
  },
  created() {
    this.theme = store.theme;
  },
  computed: {
    moreIconSrc() {
      return this.theme === 'dark' ? moreIconDark : moreIcon;
    },
  },
  methods: {
    onMoreClick() {
      this.$emit('onMoreClick');
    },
    didChangedTheme(theme) {
      this.theme = theme;
    }
  }
};
</script>

<style lang="less" scoped>
.module-container {
  margin-top: 8;
  margin-left: 6;
  margin-right: 6;
  padding-top: 12;
  background-color: #fff;
  border-radius: 6;
  overflow: hidden;
}

.module-container.dark {
  background-color: #1c1c1c;
}

.header {
  margin-left: 10;
  margin-right: 10;
  justify-content: space-between;
}

.title-container {
  line-height: 22;
  align-items: center;
}

.title {
  font-size: 18;
  font-weight: bold;
  margin-right: 8;
  color: rgba(0, 0, 0, 0.84);
}

.title.dark {
  color: rgba(255, 255, 255, 0.84);
  .clear-text-bg();
}

.more {
  font-size: 14;
  color: rgba(0, 0, 0, 0.40);
}

.more.dark {
  .clear-text-bg();
  color: rgba(255, 255, 255, 0.4);
}

.more-icon {
  width: 12;
  height: 12;
}

.clear-text-bg {
  background-color: rgba(0, 0, 0, 0);
}
</style>
