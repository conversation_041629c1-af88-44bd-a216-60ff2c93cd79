
<template>
  <k-view :class="['topic-container', theme]">
    <k-view class="top-bg">
      <k-text class="tag">{{ topic }}</k-text>
      <k-text class="title">{{ title }}</k-text>
      <k-image class="right" :src="arrowIcon"></k-image>
    </k-view>
  </k-view>
</template>

<script>
import robotIcon from '@/assets/images/ai_robot.gif';
import arrowIcon from '@/assets/images/arrow_orange.png';

export default {
  data() {
    return {
      robotIcon,
      arrowIcon,
    };
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    topic: {
      type: String,
      default: '专题',
    },
    theme: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="less" scoped>
.topic-container {
  width: 100%;
  height: 36;
  background-color: #FFFFFF;
  flex-direction: row;
  justify-content: center;
  margin-top: 8;
  margin-bottom: 8;
}

.topic-container.dark {
  background-color: #1c1c1c;
}

.top-bg {
  width: 686px;
  height: 100%;
  background: #FF661A19;
  border-radius: 4px;
  flex-direction: row;
  align-items: center;

  .tag {
    height: 16;
    padding: 2 4;
    font-size: 11;
    color: #FFFFFF;
    background: #FF661A;
    margin-left: 12;
    margin-right: 8;
    border-radius: 2;
    text-align: center;
  }

  .title {
    font-size: 14;
    color: #FF661A;
    flex: 1;
  }
}

.right {
  width: 8;
  height: 12;
  margin-right: 12;
}
</style>
