<!-- eslint-disable no-magic-numbers -->
<template>
  <k-view>
    <ErrorLayout v-if="isShowErrorLayout" :theme="theme" />
    <EmptyLayout v-else-if="isShowEmptyLayout" :theme="theme" />
    <Table
      ref="hxtable"
      v-else
      :titles="titleFields"
      :data="tableData"
      :showPageInfo="showPageInfo"
      :nightBackgroundColor="nightBackgroundColor"
      :theme="theme"
      :height="height"
      @sort="handleSort"
      @clickRow="onClickRow"
      @scroll="handleScroll"
    />
  </k-view>
</template>

<script lang="ts">
import { Table } from '@king-fisher/vue-components';
import { HttpTitleFieldMap } from '@/lib/constant';
import { defineComponent, PropType } from '@king-fisher/app-vue';
import { ParsedDataModel, TitleItemModel, HqParams, TableRowEntity, TableData } from '@/components/hqTable/hqTable';
import { HqError } from './hqTool';
import { sortOrderMap, cellType, fieldSortMode } from './hqConstant';
import secondPageDataMixin from '@/mixins/secondPageDataMixin';
import Apis from '@king-fisher/apis';
import EmptyLayout from './emptyLayout.vue';
import ErrorLayout from './errorLayout.vue';

const RequestType = {
  NONE: 0,
  TCP: 1,
  HTTP: 2,
};

export default defineComponent({
  mixins: [secondPageDataMixin],
  components: { Table, EmptyLayout, ErrorLayout },
  props: {
    // 表头字段id列表，内部生成完整表头
    headerFieldIds: {
      type: Array as PropType<Array<string>>,
      required: true,
    },
    // 表头字段name列表，内部生成完整表头
    headerFieldNames: {
      type: Array as PropType<Array<string>>,
      default: [],
      required: true,
    },
    // 默认排序id
    sortIdProp: {
      type: String,
      default: '',
    },
    // 默认排序方向
    sortOrderProp: {
      type: String,
      default: '0',
    },
    // 是否允许排序，默认允许
    sortEnable: {
      type: Boolean,
      default: true,
    },
    requestType: {
      type: Number,
      default: RequestType.NONE
    },
    // 通知外部解析tcp请求结果
    onReceiveTcpResult: {
      type: Function as PropType<(res: any) => ParsedDataModel>,
      default: null,
    },
    // 通知外部封装http请求完整url
    buildHttpUrl: {
      type: Function as PropType<(param?: HqParams) => string>,
      default: null,
    },
    // 通知外部解析http请求返回结果
    onReceiveHttpResult: {
      type: Function as PropType<(res: any) => ParsedDataModel>,
      default: null,
    },
    // 使用内部默认解析逻辑解析完数据后，通知外部解析结果
    onParseResult: {
      type: Function as PropType<(status: number, count: number, startIndex: number, total: number) => void>,
      default: null,
    },
    // 通知列表点击事件
    onItemClick: {
      type: Function as PropType<(item: TableRowEntity, list: TableRowEntity[]) => boolean>,
      default: null,
    },
    // 通知处理埋点
    onStat: {
      type: Function as PropType<(data: any) => boolean>,
      default: null,
    },
    // 组件内部使用默认逻辑处理埋点时的前缀
    statPrefix: {
      type: String,
      default: '',
    },
    // 是否显示标签，默认不显示
    enableFlag: {
      type: Boolean,
      default: false,
    },
    // 列表宽度，默认屏幕宽度，组件生成时初始化
    width: Number,
    // 列表高度
    height: {
      type: Number,
      required: true,
    },
    // 暗黑模式列表背景色
    nightBackgroundColor: {
      type: String,
      default: '#1c1c1c',
    },
    // 是否显示列表序号
    showPageInfo: {
      type: Boolean,
      default: false,
    },
    // 通知列表滚动
    onScroll: {
      type: Function as PropType<(data: any) => boolean>,
      default: null,
    },
    // 通知表头排序改变
    onSort: {
      type: Function as PropType<(data: any) => boolean>,
      default: null,
    },
    limitTableLength: {
      type: Number,
      default: 0,
    },
    theme: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      titleFields: [] as TitleItemModel[],
      tableData: {
        startIndex: 0,
        total: 0,
        list: [],
      } as TableData,
      sortId: '',
      sortOrder: '',
      startRow: 0,
      // 某次请求或解析异常
      parseError: false,
      // 某次请求返回结果为空
      parseEmpty: false,
    };
  },
  created() {
    // ios进入页面时不会执行mounted，必须在created中执行
    this.initTitleFields();
    this.loadTableData();
    // 初始化 sortId, sortOrder
    this.sortId = this.sortIdProp;
    this.sortOrder = this.sortOrderProp;
  },
  computed: {
    // 是否显示异常布局
    // 当列表为空且请求或解析异常时，显示异常布局
    isShowErrorLayout() {
      return this.tableData.list.length === 0 && this.parseError;
    },
    // 是否显示空白布局
    // 当列表为空且遇到请求返回结果为空时，显示空白布局
    isShowEmptyLayout() {
      return this.tableData.list.length === 0 && this.parseEmpty;
    },
    isDarkTheme() {
      return this.theme === 'dark';
    },
    isHttpField() {
      return Object.values(HttpTitleFieldMap).some(value => value === this.sortId);
    },
    delayTime() {
      /**
       * 由于现有的表格组件不支持初始化设置指定列
       * 将快捷排序tab点击交互拆分为两步：刷新表格数据+滑动到指定列
       * 要求滑动要在表格数据渲染完成去做，此处临时增加延迟处理
       * 延迟时间说明：高版本手机500ms、低版本手机800ms
       * 后续规划：会采用新的表格组件，支持设置初始列，该页面改造为DSL页面
       */
      const sysVersion = Apis.getSystemInfoSync()?.version ?? 0;
      const divideVersion = __GLOBAL__.isIos ? 13 : 10;
      if (Number(sysVersion) < divideVersion) {
        return 800;
      }
      return 500;
    }
  },
  methods: {
    changeColumn(index: number) {
      // 快捷排序规则，更新表头排序选中列
      this.updateTitleFields();
      // hxtable更新title之后需要刷新tableData
      this.tableData = JSON.parse(JSON.stringify(this.secondPageTableData));

      setTimeout(() => {
        this.$refs.hxtable.table.table.scrollToColumn(index, false);
      }, this.delayTime);
    },
    // 初始化标题栏字段列表
    initTitleFields() {
      if (this.headerFieldIds.length <= 0) {
        return;
      }
      if (this.headerFieldIds.length !== this.headerFieldNames.length) {
        return;
      }

      const fields: Array<TitleItemModel> = [{
        title: '名称代码',
        // 排序id，数字
        sortId: '0',
        // 当前item视图类型，0默认一行，1 两行，2 两行带图标
        type: cellType.twoLine,
        sort: fieldSortMode.disable
      } as TitleItemModel].concat(
        this.headerFieldIds.map((fieldId: string, index: number) => {
          const title = this.headerFieldNames[index];
          return {
            title,
            sortId: fieldId,
            sort: this.sortEnable ? fieldSortMode.enable : fieldSortMode.disable,
            type: cellType.oneLine,
          } as TitleItemModel;
        })
      );

      if (!this.sortEnable) {
        this.titleFields = fields;
        return;
      }

      if (this.sortIdProp) {
        let sortFieldIndex = this.headerFieldIds.findIndex(item => item === this.sortIdProp);
        if (-1 === sortFieldIndex) {
          sortFieldIndex = 1;
        }
        fields[sortFieldIndex + 1].defaultSortMode = this.sortOrderProp;
      } else {
        if (fields.length > 1) {
          fields[1].defaultSortMode = this.sortOrderProp;
        }
      }
      this.titleFields = fields;
    },
    updateTitleFields() {
      this.titleFields = this.titleFields.map((item: TitleItemModel) => {
        if (item.sortId === this.sortId) {
          item.defaultSortMode = this.sortOrder;
        } else {
          item.defaultSortMode = sortOrderMap.default;
        }
        return item;
      });
    },

    // 发起行情请求
    loadTableData(param?: HqParams) {
      // 发起新的请求前，先清空原请求结果标志
      this.parseError = false;
      this.parseEmpty = false;

      if (this.requestType === RequestType.TCP) {
        const sortId = this.sortId || this.sortIdProp;
        const sortOrder = this.sortOrder || this.sortOrderProp;
        this.requestSeconedPageData(sortId, sortOrder);
      } else if (this.requestType === RequestType.HTTP) {
        this.startHttpRequest(param);
      } else {
        this.onParseError();
        // 参数错误，上报异常
        throw new HqError('loadTableData param error');
      }
    },

    // 发起http请求
    startHttpRequest(param?: HqParams) {
      let httpUrl: string | undefined;

      // 调用外部函数，获取请求url
      if (this.buildHttpUrl) {
        httpUrl = this.buildHttpUrl(param);
      }

      if (httpUrl) {
        // 发起http请求
        this.doHttpReq(httpUrl);
      } else {
        // 外部没有提供获取url的方法，报异常，并显示异常布局
        this.onParseError();
        throw new HqError('startHttpRequest Error url is empty', this.tcpReqBody);
      }
    },

    // 发起http请求
    doHttpReq(url: string) {
      Apis.request({
        url,
        method: 'GET',
        success: (res: any) => {
          let parsedData: ParsedDataModel | undefined;

          if (this.onReceiveHttpResult) {
            const result = this.onReceiveHttpResult(res);
            if (result.handled) {
              parsedData = result;
            }
          }

          this.handleParseResult(parsedData);
        },
        fail: (res: any) => {
          this.onParseError();
          throw new HqError('doHttpReq 请求失败', res);
        },
      });
    },

    // 解析tcp行情返回数据
    // 如果外部处理解析逻辑，控件内部不再继续处理
    onParseTcpRes(res: any) {
      let parsedData: ParsedDataModel | undefined;

      if (this.onReceiveTcpResult) {
        const result = this.onReceiveTcpResult(res);
        if (result.handled) {
          parsedData = result;
        }
      }

      if (!parsedData) {
        // 使用内部解析逻辑解析tcp数据
        parsedData = this.formatTableParams(res);
      }

      this.handleParseResult(parsedData);
    },

    // 处理解析结果
    handleParseResult(parsedData?: ParsedDataModel) {
      if (!parsedData || !parsedData.handled) {
        // 外部和内部都没有解析，显示异常布局
        this.onParseError();
      } else {
        if (parsedData.flag === -1) {
          // 解析异常，显示异常布局
          this.onParseError();
        } else {
          if (parsedData.data && parsedData.data.list.length > 0) {
            this.startRow = parsedData.data.startIndex;
            this.tableData = parsedData.data;
            this.$emit('updatedTableData', this.tableData);
          } else {
            // 列表为空，显示空白布局
            this.onParseEmpty();
          }
        }
      }
    },

    // 外部和内部没有解析或解析失败，如果此时当前列表为空时，显示异常布局
    onParseError() {
      this.parseError = true;
      this.$emit('onParseError');
    },

    // 解析结果为空，如果此时当前列表为空时，显示空白布局
    onParseEmpty() {
      this.parseEmpty = true;
      this.$emit('onParseEmpty');
    },

    // 监听列表表头点击排序事件
    handleSort(item: any) {
      if (this.onSort && this.onSort(item)) {
        // 外部处理表头点击事件，内部不再处理
        return;
      }
      const itemIndex: number = this.titleFields.findIndex(value => value.sortId === `${item.sortId}`);
      this.$emit('onHandleSort', item.sortMode, itemIndex);

      if (item) {
        // 更新sortId, sortOrder
        this.sortId = `${item.sortId}` || this.sortId;
        // ios 上sortMode为asc或desc，需转换为0|1
        this.sortOrder = item.sortOrder ?? (sortOrderMap[String(item.sortMode)] || this.sortOrder);

        if (this.requestType === RequestType.TCP && this.isHttpField) {
          // http数据，非实时更新，自己处理排序逻辑
          this.httpFieldSortAction(this.sortId, this.sortOrder);
          return;
        }

        this.loadTableData({
          sortId: this.sortId,
          sortOrder: this.sortOrder,
          startRow: 0,
        });
        return;
      }

      this.loadTableData();
    },

    //监听列表滚动事件
    handleScroll(item: any) {
      if (this.onScroll && this.onScroll(item)) {
        // 外部处理滚动事件，内部不再处理
        return;
      }

      if (item.startIndex === this.startRow) {
        // startIndex相等说明是横向滑动，不要重复请求数据，否则列表数据会闪动
        return;
      }

      // 滚动时，使用初始或排序时更新的 sortId 和 sortOrder
      this.loadTableData({
        sortId: this.sortId,
        sortOrder: this.sortOrder,
        startRow: item.startIndex,
      });
    },

    // 监听列表点击事件
    onClickRow(item: TableRowEntity) {
      if (this.onItemClick) {
        this.onItemClick(item, this.tableData.list);
      }
    },
  },
});
</script>
