<template>
  <k-view class="hq-error-root">
    <k-image class="img" :src="errorIcon"></k-image>
    <k-text :class="['text', theme]">数据异常，请稍后重试</k-text>
  </k-view>
</template>

<script>
import errorIcon from '../../assets/images/data_error.png';
import errorIconDark from '../../assets/images/data_error_dark.png';

export default {
  name: 'hq-error-layout',
  props: {
    theme: {
      type: String,
      default: '',
      required: true,
    },
  },
  computed: {
    errorIcon() {
      return this.theme === 'dark' ? errorIconDark : errorIcon;
    },
  },
};
</script>

<style scoped>
.hq-error-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.img {
  width: 240px;
  height: 240px;
  margin-top: 350px;
}

.text {
  color: rgba(0, 0, 0, 0.84);
  font-size: 16;
}

.text.dark {
  color: rgba(255, 255, 255, 0.84);
}
</style>
