import { fieldIds } from './hqConstant';
import { HttpTitleFieldMap } from '@/lib/constant.js';

const DATE_LENGTH = 8;

export class HqError extends Error {
  constructor(name, message) {
    super();
    this.name = name;
    this.message = typeof message === 'string' ? message : JSON.stringify(message);
  }
}

export const HqColor = {
  valueColor: {
    light: '#000000D6',
    dark: '#FFFFFFD6',
  },
  valueColorArgb: {
    light: '#D6000000',
    dark: '#D6FFFFFF',
  },
  subValueColor: {
    light: '#00000052',
    dark: '#FFFFFF52',
  },
  subValueColorArgb: {
    light: '#52000000',
    dark: '#52FFFFFF',
  },
  redColor: {
    light: '#ff2436',
    dark: '#ff2436',
  },
  greenColor: {
    light: '#07ab4b',
    dark: '#07ab4b',
  },
  blind: {
    light: '#0ca3b0',
    dark: '#15a9b6',
  },
};

// 没有亿或者万作为单位的字段，保留整数
const fixIntegerFields = new Set([
  // 成交量,总手
  '13',
  // 成交额
  '19',
  // 现手
  '49',
  // 市场份额
  '407',
  // 总市值|规模
  fieldIds.scale,
  HttpTitleFieldMap.scale,
  HttpTitleFieldMap.heat,
]);

// 保留三位小数的字段
const fix3DecimalFields = new Set([
  // 最新
  '10',
  // 涨跌
  '34387',
  // 涨跌
  '34821',
]);

// 保留两位位小数的字段
const fix2DecimalFields = new Set([
  // 量比
  '34311',
]);

// 保留两位小数并且加上百分号
const fixPercentFields = new Set([
  // 涨速
  '48',
  // 近一月涨幅
  '3252',
  // 涨幅, 4104
  '33001',
  // 溢价率
  '33004',
  // 换手
  '34312',
  // 振幅
  '34314',
  // 涨幅, 4106
  '34362',
  // 近一周涨幅
  '34376',
  // 涨幅, 1264
  '34818',
  // 近3月涨幅
  '34850',
  // 近1年涨幅
  '34852',
  // 近6月涨幅
  '34851',
  // 业绩相似度
  '36151',
  // 持仓占比
  fieldIds.positionRatio,
  // 上市至今涨幅
  '36153',
  HttpTitleFieldMap.similar,
  HttpTitleFieldMap.investRate,
  HttpTitleFieldMap.operFee,
]);

// 日期格式化
const dateFields = new Set([
  // 上市日期
  '36154',
]);

// 数值单位 亿
const unitYi = 100000000.0;
// 万
const unitWan = 10000.0;

// 小数点位数
const float4Fix = 4;
const float3Fix = 3;
const float2Fix = 2;
const float0Fix = 0;

// 格式化成交额，总市值等
export function formatAmount(value) {
  if (isNaN(value)) {
    if (!value) {
      // value 为 undefined 或空时，返回 --
      return '--';
    }
    return value;
  }

  // 大于1亿以亿为单位，大于万以万为单位，保留两位小数，小于1万返回原值
  if (value >= unitYi) {
    const num = parseFloat(value / unitYi).toFixed(float2Fix);
    return `${num}亿`;
  } else if (value >= unitWan) {
    const num = parseFloat(value / unitWan).toFixed(float0Fix);
    return `${num}万`;
  } else {
    // 小于1万时返回整数
    return Math.round(value);
  }
}

// 整理小数位数
export function fixDecimals(value, fixedNumber = float2Fix) {
  if (value) {
    const fixedValue = parseFloat(value).toFixed(fixedNumber);
    return isNaN(fixedValue) ? '--' : fixedValue;
  } else {
    return '0';
  }
}

// 格式化日期 yyyymmdd => yyyy-mm-dd
function formatDate(yyyymmdd) {
  const value = parseInt(yyyymmdd, 10);
  if (!!yyyymmdd && yyyymmdd.length === DATE_LENGTH && !isNaN(value)) {
    return value.toString().replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
  } else {
    return '--';
  }
}

// 保留两位小数的百分数
export function fix2DicimalPercent(value) {
  const percentValue = fixDecimals(value, float2Fix);
  return `${percentValue}${percentValue === '--' ? '' : '%'}`;
};

// 保留四位小数
export function fix4Dicimal(value) {
  return fixDecimals(value, float4Fix);
};

// 格式化列表项数值
export function formatValue(value, fieldId) {
  if (fixIntegerFields.has(fieldId) && !isNaN(value)) {
    return formatAmount(value);
  } else if (fix3DecimalFields.has(fieldId)) {
    return fixDecimals(value, float3Fix);
  } else if (fix2DecimalFields.has(fieldId)) {
    return fixDecimals(value, float2Fix);
  } else if (fixPercentFields.has(fieldId)) {
    return fix2DicimalPercent(value);
  } else if (dateFields.has(fieldId)) {
    return formatDate(value);
  } else {
    return value;
  }
}

// 如果是android，使用argb，否则使用rgba
export function getValueColor(isDarkTheme) {
  if (Hummer.env.platform === 'Android') {
    return isDarkTheme ? HqColor.valueColorArgb.dark : HqColor.valueColorArgb.light;
  } else {
    return isDarkTheme ? HqColor.valueColor.dark : HqColor.valueColor.light;
  }
}

// 如果是android，使用argb，否则使用rgba
export function getSubValueColor(isDarkTheme) {
  if (Hummer.env.platform === 'Android') {
    return isDarkTheme
      ? HqColor.subValueColorArgb.dark
      : HqColor.subValueColorArgb.light;
  } else {
    return isDarkTheme
      ? HqColor.subValueColor.dark
      : HqColor.subValueColor.light;
  }
}

// 获取类似涨跌幅一样带红绿色的字段字体色
export function getZdfColor(value, isDarkTheme, isBlind) {
  const fixValue = Number(fixDecimals(value, float4Fix));
  if (fixValue < 0) {
    // 色盲模式
    if (isBlind) {
      return isDarkTheme ? HqColor.blind.dark : HqColor.blind.light;
    }
    return isDarkTheme ? HqColor.greenColor.dark : HqColor.greenColor.light;
  }
  if (fixValue > 0) {
    return isDarkTheme ? HqColor.redColor.dark : HqColor.redColor.light;
  }
  return getValueColor(isDarkTheme);
}

export const colorHeaders = new Set([
  // 最新
  '10',
  // 涨速
  '48',
  // 近一月涨幅
  '3252',
  // 涨幅, 4104
  '33001',
  // 溢价率
  '33004',
  // 量比
  '34311',
  // 近一周涨幅
  '34376',
  // 涨跌
  '34387',
  // 涨幅
  '34818',
  // 涨跌
  '34821',
  // 近3月涨幅
  '34850',
  // 近1年涨幅
  '34852',
  // 近6月涨幅
  '34851',
  // 上市至今涨幅
  '36153',
]);

// 格式化处理列表数据项颜色
export function getCellColor(value, valueType, isDarkTheme) {
  if (colorHeaders.has(valueType)) {
    return getZdfColor(value, isDarkTheme);
  }
  return getValueColor(isDarkTheme);
}
