<template>
  <k-view class="hq-empty-root">
    <k-text :class="['text', theme]">暂无内容</k-text>
  </k-view>
</template>

<script>
export default {
  name: 'hq-empty-layout',
  props: {
    theme: {
      type: String,
      default: '',
      required: true,
    },
  },
};
</script>

<style scoped>
.hq-empty-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.text {
  color: rgba(0, 0, 0, 0.84);
  font-size: 16;
  margin-top: 268;
}

.text.dark {
  color: rgba(255, 255, 255, 0.84);
}
</style>
