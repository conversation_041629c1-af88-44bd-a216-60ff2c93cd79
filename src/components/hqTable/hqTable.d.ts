
// 表头列表单个数据项格式
export interface TitleItemModel {
  // 股票名称
  readonly title: string,
  // 排序字段id
  sortId: string,
  // 控制表头对应cell数据展示格式，为1时展示两行，包含标签，默认展示1行
  type: string,
  // 设置默认排序顺序，当sort为1时生效，降序0,升序1，默认空字符串无排序
  defaultSortMode?: string,
  // 控制排序三角形是否显示，以及是否可点击触发排序，为1时显示。首列不展示
  sort?: string,
}

export interface TcpRequestBody {
  readonly protocolId: string,
  readonly pageId: string,
  readonly onlineId: string,
  readonly requestDic: string,
}

// 因列表滚动，排序改变等造成请求参数变化时，新的请求参数
export interface HqParams {
  sortId?: string,
  sortOrder?: string,
  startRow?: number,
}

// 列表单个cell的数据格式
export interface TableCellEntity {
  value: string,
  valueColor?: string,
  subValue?: string,
  subValueColor?: string,
  // 用于判断是否展示市场标签
  marketId?: string,
  // 市场标签列表
  stockKinds?: string[],
}

// 列表单行数据格式
// index和stockCode原生没有解析，透传，用于埋点
export interface TableRowEntity {
  index: number,
  // marketid
  id: string,
  stockCode: string,
  bgColor?: string,
  // 各字段值 cell 列表
  value: TableCellEntity[],
}

// 列表完整数据
export interface TableData {
  list: TableRowEntity[],
  // list 中首项数据在total数据中的序号
  startIndex: number,
  // 列表全部数据数量，非已加载数据数量
  total: number,
}

// 解析后数据的格式
export interface ParsedDataModel {
  // 标识是否已处理解析逻辑
  readonly handled: boolean,
  // 标识解析处理结果，0表示成功，-1表示失败
  readonly flag: 0 | -1,
  // 解析失败时，可不指定 data
  data?: TableData,
}
