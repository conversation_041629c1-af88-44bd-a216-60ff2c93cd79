import { HttpTitleFieldMap, TcpFieldMap } from "@/lib/constant";

// 排序相关
export const sortOrderMap : { [key: string] : string} = {
  0: '0',
  1: '1',
  desc: '0',
  asc: '1',
  default: '2',
};

// 行情列表单元格类型
export const cellType = {
  oneLine: '0',
  twoLine: '1',
  twoLineIcon: '2'
};

// 表头模式，是否允许排序
export const fieldSortMode = {
  enable: '1',
  disable: '0'
};

// 字段列表
export const fieldIds = {
  // 总市值|规模
  scale: '36163',
  // 持仓占比-场内ETF
  positionRatio: '36164',
  // 溢价率
  premiumRate: '33004',
  // 业绩相似度
  similar: '36151',
  // ETF涨幅
  riseEtf: '33001',
};

export const QuickSortTabIndexConfig = [
  {
    title: '溢价率榜',
    sortId: TcpFieldMap.premiumRate,
    sortOrder: sortOrderMap.asc,
    topTag: '更划算',
  },
  {
    title: '成交额榜',
    sortId: TcpFieldMap.tradeVol,
    sortOrder: sortOrderMap.desc,
    topTag: '流动性好',
  },
  {
    title: '热度榜',
    sortId: HttpTitleFieldMap.heat,
    sortOrder: sortOrderMap.desc,
    topTag: '更热门',
  },
  {
    title: '规模榜',
    sortId: HttpTitleFieldMap.scale,
    sortOrder: sortOrderMap.desc,
    topTag: null,
  },
  {
    title: '价格榜',
    sortId: TcpFieldMap.latestPrice,
    sortOrder: sortOrderMap.asc,
    topTag: '门槛低',
  },
  {
    title: '运作费率',
    sortId: HttpTitleFieldMap.operFee,
    sortOrder: sortOrderMap.asc,
    topTag: '成本低',
  }
];

export const QuickSortTabPlateConfig = [
  ...QuickSortTabIndexConfig,
  {
    title: '持仓占比',
    sortId: HttpTitleFieldMap.investRate,
    sortOrder: sortOrderMap.desc,
    topTag: '跟踪度高',
  }
];
