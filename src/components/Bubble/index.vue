<template>
  <view class="bubble-back" @tap="clickAction">
    <view :style="bubbleStyle">
      <image class="bubble-arrow" :style="arrowStyle" :src="TopArrow"></image>
      <view class="bubble-rect">
        <text class="bubble-text">{{ content }}</text>
        <text class="bubble-next-step">{{ buttonText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import TopArrow from '@/assets/images/bubble_top_arrow.png';

export default {
  props: {
    content: {
      type: String
    },
    positionStyle: {
      type: Object
    },
    // 箭头位置
    arrorwLocation: {
      type: String,
      default: 'left'
    },
    buttonText: {
      type: String
    }
  },
  computed: {
    bubbleStyle() {
      const bubbleStyle = this.positionStyle ?? {};
      bubbleStyle['position'] = 'absolute';

      switch (this.arrorwLocation) {
        case 'left':
          bubbleStyle['align-items'] = 'flex-start';
          break;
        case 'right':
          bubbleStyle['align-items'] = 'flex-end';
          break;
        default:
          break;
      }
      return bubbleStyle;
    },
    arrowStyle() {
      switch (this.arrorwLocation) {
        case 'center':
          return { left: 98 };
        case 'right':
          return { right: 8 };
        default:
          return {};
      }
    }
  },
  data() {
    return {
      TopArrow
    };
  },
  methods: {
    clickAction() {
      this.$emit('bubbleClick');
    }
  },
};
</script>

<style lang="less" scoped>
.bubble-back {
  position: absolute;
  width: 100%;
  height: 100%;
}

.bubble-arrow {
  width: 10;
  height: 4;
}

.bubble-rect {
  align-items: flex-start;
  background-color: #3B3B3B;
  border-radius: 6;
  padding: 8 16 8 16;
  height: 100%;
}

.bubble-text {
  height: 42;
  line-height: 21;
  font-family: PingFangSC-Regular;
  font-size: 14;
  color: #FFFFFF;
  font-weight: medium;
}

.bubble-next-step {
  top: 8;
  left: 124;
  width: 52;
  height: 20;
  background-color: white;
  border-radius: 2;
  font-family: PingFangSC-Regular;
  font-size: 12;
  text-align: center;
  color: rgba(0, 0, 0, 0.84);
  font-weight: medium;
}
</style>
