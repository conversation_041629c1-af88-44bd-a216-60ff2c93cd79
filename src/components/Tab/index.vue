<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-03 16:48:03
 * @Description: Tab组件
-->
<template>
  <view ref="scrollContainer" class="posi-r">
    <scroller
      ref="scroller"
      scrollDirection="horizontal"
      :forceSelfScroll="true"
      :class="['head-list ai-c', theme]"
    >
      <view
        :class="[
          'flex jc-c ai-c',
          tabContainerClass,
          theme,
          !isIndicatorStyle && activeHeader === index ? 'container-active' : '',
        ]"
        :style="{ ...tabWidth, margin: getTabMargin(index) }"
        @click="tabClick(index)"
        :ref="'tabItem' + index"
        v-for="(tab, index) in tabList"
        :key="index"
      >
        <!-- 文字tab之间的分割线 -->
        <view v-if="isTextStyle && index !== 0" :class="['text-item-divider', theme]"></view>

        <text
          :class="[tabItemClass, activeHeader === index ? 'text-active' : '', theme]"
          textLineClamp="1"
          :style="itemMaxWidth > 0 ? { maxWidth: itemMaxWidth } : {}"
        >
          {{ tab }}
        </text>
        <!-- 底部指示器，只有为indicator样式时展示 -->
        <view v-if="isIndicatorStyle && activeHeader === index" class="active-bottom posi-a" />
      </view>
    </scroller>
    <image class="mask" ref="mask" :src="etfMaskIconUrl"></image>
  </view>
</template>

<script lang="ts">
import etfMaskDarkIcon from '@/assets/images/etf_mask_dark.png';
import etfMaskIcon from '@/assets/images/etf_mask.png';

const INDICATOR_MARGIN = 24;
const BORDER_MARGIN = 8;
const DoubleCount = 2;
const DUMP_INDEX = -1;

export default {
  name: 'Tab',
  props: {
    /// 主题
    theme: {
      type: String,
      default: '',
    },
    /// tab列表
    tabList: {
      type: Array,
      default: () => [],
      validator(value: unknown[]) {
        // 自定义验证器确保数组的每个元素都是字符串类型
        return value.every(item => typeof item === 'string');
      },
    },
    /**
     * @description: 支持三种样式
     * indicator: 无边框，选中后文本加粗，底部有指示条，一级导航采用
     * solid: 无边框，实心背景色，选中后颜色高亮，二级导航采用
     * border: 矩形边框，选中后颜色高亮，三级导航采用
     * text: 纯文字，选中时加粗，使用竖线分割
     */
    tabType: {
      type: String,
      default: 'indicator',
    },
    /**
     * @description: tab的分布方式
     * list: 自适应排列
     * fill: 均分填充
     */
    distribution: {
      type: String,
      default: 'list',
    },
    /**
     * @description: 默认选中下标
     */
    selectedIndex: {
      type: Number,
      default: 0,
    },
    /**
     * @description: tab容器左右padding，默认16
     */
    padding: {
      type: Number,
      default: 16,
    },
    /**
     * @description: tab之间的间距, -1表示使用默认值
     * 默认indicator中间间距24，， border 中间间距8
     */
    margin: {
      type: Number,
      default: -1,
    },
    /**
     * @description: item最大宽度
     */
    itemMaxWidth: {
      type: Number,
      default: -1,
    },
  },
  computed: {
    etfMaskIconUrl() {
      return this.theme === 'dark' ? this.etfMaskDarkIcon : this.etfMaskIcon;
    },
    isIndicatorStyle() {
      return this.tabType === 'indicator';
    },
    isTextStyle() {
      return this.tabType === 'text';
    },
    isSolidStyle() {
      return this.tabType === 'solid';
    },
    isFillDistribution() {
      return this.distribution === 'fill';
    },
    tabMargin() {
      if (this.margin > 0) {
        return this.margin / DoubleCount;
      }
      return (this.isIndicatorStyle ? INDICATOR_MARGIN : BORDER_MARGIN) / DoubleCount;
    },
    /// tab容器样式
    tabContainerClass() {
      if (this.isIndicatorStyle) {
        return 'posi-r tab-item';
      }
      if (this.isTextStyle) {
        return 'text-item';
      }
      let borderClass = 'border-item-container';
      if (this.isSolidStyle) {
        borderClass += ' solid';
      }
      return borderClass;
    },
    tabWidth() {
      if (!this.isFillDistribution || !this.tabList.length) {
        return { width: 'auto' };
      }
      const margin = this.tabMargin * DoubleCount;
      const marginCount = this.tabList.length - 1;
      const contentWidth = this.containerWidth - this.padding * DoubleCount - margin * marginCount;
      const tabWidth = contentWidth / this.tabList.length;
      return { width: tabWidth };
    },
    /// tab文本样式
    tabItemClass() {
      if (this.isIndicatorStyle) {
        return 'indicator-item-text ta-c';
      }
      if (this.isTextStyle) {
        return 'text-item-text';
      }
      let borderClass = 'ml-n12 mr-n12 border-item-text';
      if (this.isSolidStyle) {
        borderClass += ' solid';
      }
      return borderClass;
    },
  },
  data() {
    return {
      etfMaskDarkIcon,
      etfMaskIcon,
      activeHeader: Number(this.selectedIndex),
      canScroll: true,
      containerWidth: __GLOBAL__.screenWidth,
    };
  },
  watch: {
    tabList: {
      handler(newValue) {
        this.$nextTick(() => {
          this.checkCanScroll(newValue.length);
        });
      },
    },
    selectedIndex: {
      handler(index) {
        const indexValue = Number(index);
        if (this.activeHeader === indexValue) {
          return;
        }
        this.activeHeader = indexValue;
        this.$nextTick(() => {
          this.scrollToActiveItem();
        });
      },
    },
  },
  methods: {
    getTabMargin(index: number) {
      if (this.isTextStyle) {
        // 文字类型的tab，tab之间没有margin，使用竖线分割
        return '0';
      }

      // 中间元素取,头尾元素距取padding
      return `${this.isIndicatorStyle ? 0 : BORDER_MARGIN} ${
        index === this.tabList.length - 1 ? this.padding : this.tabMargin
      } ${this.isIndicatorStyle ? 0 : BORDER_MARGIN} ${
        index === 0 ? this.padding : this.tabMargin
      }`;
    },
    tabClick(index: number) {
      this.activeHeader = index;
      this.scrollToActiveItem();
      this.$emit('onTabClick', index);
    },
    scrollToActiveItem() {
      if (this.isFillDistribution || !this.canScroll) {
        return;
      }
      const tabId = `tabItem${this.activeHeader}`;
      const scroller = this.$refs.scroller;
      this.$refs[tabId]?.element?.getRect(rect => {
        this.$refs.scrollContainer?.element?.getRect(scrollerRect => {
          const offset = rect.right - scrollerRect.width / DoubleCount;
          if (offset < 0) {
            scroller.scrollToTop();
          } else {
            scroller.scrollTo(offset, rect.top);
          }
        });
      });
    },
    checkCanScroll(tabCount: number) {
      if (tabCount <= 0) {
        this.canScroll = false;
        return;
      }
      const tabId = `tabItem${tabCount - 1}`;
      const container = this.$refs.scrollContainer.element;
      this.$nextTick(() => {
        this.$refs[tabId]?.element.getRect(rect => {
          if (!container) {
            return;
          }
          container.getRect(scrollerRect => {
            this.containerWidth = scrollerRect.width;
            this.canScroll = rect.right > scrollerRect.width;
          });
        });
      });
    },
    resetSelectIndex() {
      this.activeHeader = DUMP_INDEX;
    }
  },
  mounted() {
    this.checkCanScroll(this.tabList.length);
    this.$refs.mask.element.enabled = false;
  },
};
</script>

<style lang="less" scoped>
.tab-item {
  height: 40;
}

.text-item {
  background-color: white;
  flex-direction: row;
}

.text-item-text {
  margin: 0 12;
  font-size: 14;
  height: 18;
  line-height: 18;
  color: rgba(0, 0, 0, 0.6);
  font-weight: normal;

  &.dark {
    color: rgba(255, 255, 255, 0.6);
  }

  &.text-active {
    color: rgba(0, 0, 0, 0.84);
    font-weight: bold;
  }
}

.text-item-divider {
  width: 1;
  height: 12;
  background-color: rgba(0, 0, 0, 0.08);

  &.dark {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

.indicator-item-text {
  font-size: 16;
  color: rgba(0, 0, 0, 0.6);
  height: 20;
  font-weight: normal;
}

.indicator-item-text.dark {
  color: rgba(255, 255, 255, 0.6);
}

.indicator-item-text.text-active {
  color: rgba(0, 0, 0, 0.84);
  font-weight: bold;
}

.indicator-item-text.text-active.dark {
  color: rgba(255, 255, 255, 0.84);
}

.head-list {
  background-color: #ffffff;
}

.head-list.dark {
  background-color: #1c1c1c;
}

.active-bottom {
  width: 12;
  height: 3;
  background: #ff2436;
  border-radius: 1;
  bottom: 3;
}

.border-item-container {
  justify-content: center;
  height: 30;
  align-items: center;
  border-radius: 4;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.24);
  background-color: #fff;
}
.border-item-container.dark {
  background-color: #1c1c1c;
  border-color: rgba(255, 255, 255, 0.24);
}

.border-item-container.solid {
  border-width: 0;
  background-color: rgba(0, 0, 0, 0.04);
}

.border-item-container.solid.dark {
  border-width: 0;
  background-color: rgba(255, 255, 255, 0.06);
}

.border-item-container.container-active {
  border-width: 0;
  background-color: rgba(255, 36, 54, 0.1);
}
.border-item-text {
  font-size: 14;
  height: 20;
  color: rgba(0, 0, 0, 0.6);
  font-weight: medium;
}
.border-item-text.dark {
  color: rgba(255, 255, 255, 0.6);
  background-color: rgba(0, 0, 0, 0);
}
.border-item-text.text-active {
  color: #ff2436;
  font-weight: bold;
}
.border-item-text.text-active.dark {
  color: #ff2436;
  font-weight: bold;
}

.mask {
  pointer-events: none;
  width: 24;
  height: 100%;
  right: 0;
  position: absolute;
}
</style>
