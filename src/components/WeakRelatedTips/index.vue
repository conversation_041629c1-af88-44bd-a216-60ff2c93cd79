<template>
  <k-view :class="['weak-container', theme]">
    <k-image class="help" :src="helpIconSrc"></k-image>
    <k-text :class="['title', theme]">{{ title }}</k-text>
    <k-image class="right" :src="arrowIconSrc"></k-image>
  </k-view>
</template>

<script>
import arrowIcon from '@/assets/images/container_more.png';
import arrowIconDark from '@/assets/images/container_more_dark.png';
import helpIcon from '@/assets/images/help.png';
import helpIconDark from '@/assets/images/help_dark.png';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
  },
  computed: {
    helpIconSrc() {
      return this.theme === 'dark' ? helpIconDark : helpIcon;
    },
    arrowIconSrc() {
      return this.theme === 'dark' ? arrowIconDark : arrowIcon;
    },
  },
};
</script>

<style lang="less" scoped>
.weak-container {
  height: 32;
  width: 100%;
  flex-direction: row;
  align-items: center;

  .help {
    width: 16;
    height: 16;
    opacity: 0.4;
    margin-left: 10;
    margin-right: 2;
  }

  .title {
    font-size: 14;
    color: rgba(0, 0, 0, 0.40);
    text-overflow: ellipsis;
    margin-right: 2;
  }

  .right {
    width: 12;
    height: 12;
  }
}

.title.dark {
  color: rgba(255, 255, 255, 0.40);

}

</style>
