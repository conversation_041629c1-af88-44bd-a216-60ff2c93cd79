<template>
  <k-view :class="['container', theme]">
    <k-view class="header">
      <k-view class="header_item header_1" @click="onHeaderClick(0, false)">
        <k-text :class="['header_item_text', theme]">股票名称</k-text>
      </k-view>
      <k-view class="header_item header_2" @click="onHeaderClick(1, false)">
        <k-text :class="['header_item_text', 'ta-r', theme]">{{ secondColumnTitle }}</k-text>
      </k-view>
      <k-view v-if="headerList.length >= 1" @click="onHeaderClick(headerList[0].key, headerList[0].showHelp)" class="header_item header_3">
        <k-image v-if="headerList[0].showHelp" class="header_item_image" :src="helpIconSrc"></k-image>
        <k-text textLineClamp="1" :class="['header_item_text', getHeaderClass(headerList[0].name), 'ta-r', theme]">{{
            headerList[0].name
        }}
        </k-text>
      </k-view>
      <k-view v-if="headerList.length >= 2" @click="onHeaderClick(headerList[1].key, headerList[1].showHelp)" class="header_item header_4">
        <k-image v-if="headerList[1].showHelp" class="header_item_image" :src="helpIconSrc"></k-image>
        <k-text :class="['header_item_text', 'ta-r', theme]">{{ headerList[1].name }}</k-text>
      </k-view>
      <k-view :class="['line', theme]"></k-view>
    </k-view>
    <k-view class="list">
      <RelatedFundCell v-for="data, index in dataList" @click="onCellClick(index)" :headerList="headerList" :item="data"
        :theme="theme" :key="index">
      </RelatedFundCell>
    </k-view>
    <TagMoreButton style="height: 42; width: 100%;" :show-tag="moreButtonTagShow" :theme="theme" @onMoreClick="onMoreClick"></TagMoreButton>
  </k-view>
</template>

<script>
import helpIcon from '@/assets/images/help.png';
import helpIconDark from '@/assets/images/help_dark.png';
import RelatedFundCell from '@/components/RelatedFundCell/index.vue';
import TagMoreButton from '@/components/MoreButton/newFuncTagMoreButton.vue';
import Apis from '@king-fisher/apis';
import { isValidSrting } from '@/utils/commonTools.ts';
import { FuturesSceneSecondType, SceneFirstType } from '@/lib/SceneTypeDefines';
import { store } from '@/lib/store';

const MORE_BUTTON_TAG_SHOW = 'more_button_tag_show';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
    dataList: {
      type: Array,
      default() {
        return [];
      },
    },
    headerList: {
      type: Array,
      default() {
        return [];
      },
    },
    type: {
      type: String,
      required: true,
    },
  },
  computed: {
    helpIconSrc() {
      return this.theme === 'dark' ? helpIconDark : helpIcon;
    },
  },
  components: {
    RelatedFundCell,
    TagMoreButton
  },
  data() {
    return {
      moreButtonTagShow: false,
      secondColumnTitle: this.type === 'outer' ? "近3月" : "涨幅"
    };
  },
  created() {
    const hasShow = isValidSrting(Apis.getStorageSync(MORE_BUTTON_TAG_SHOW));
    const typeCheck = this.type === 'inner' && store.firstSceneType !== SceneFirstType.hkStock && store.firstSceneType !== SceneFirstType.usStock;
    this.moreButtonTagShow = typeCheck && !hasShow;
    if (!typeCheck && store.secondSceneType === FuturesSceneSecondType.bondFutures) {
      this.secondColumnTitle = "近1年"
    }
  },
  methods: {
    getHeaderClass(title) {
      const maxLength = 4;
      if (title.length > maxLength) {
        return 'header_text_3';
      }
      return '';
    },
    onMoreClick() {
      Apis.setStorageSync(MORE_BUTTON_TAG_SHOW, '1');
      this.moreButtonTagShow = false;

      this.$emit('onMoreClick');
    },
    onHeaderClick(key, isShowHelpTip) {
      if (isShowHelpTip) {
        this.$emit('onHeaderClick', key);
      }
    },
    onCellClick(index) {
      this.$emit('onCellClick', index);
    }
  },
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  width: 100%;
  height: 80px;
  padding-left: 10;
  padding-right: 10;
  flex-direction: row;
  justify-content: flex-start;
  position: relative;
}

.header_1 {
  width: 206px;
  height: 100%;
}

.header_item.header_2 {
  .header_end();
}

.header_item.header_3 {
  .header_end();
}

.header_item.header_4 {
  .header_end();
}

.header_end {
  margin-left: 16px;
  width: 144px;
  height: 100%;
  justify-content: flex-end;
}

.header_item {
  flex-direction: row;
  align-items: center;
}

.header_item_image {
  width: 12;
  height: 12;
  flex-shrink: 0;
}

.header_item_text {
  font-size: 14;
  color: rgba(0, 0, 0, 0.60);
}

.header_item_text.header_text_3 {
  font-size: 10;
}

.header_item_text.dark {
  color: rgba(255, 255, 255, 0.60);
}

.list {
  flex-direction: column;
  width: 100%;
  padding-left: 10;
  padding-right: 10;
}

.line {
  position: absolute;
  width: 726px;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  bottom: 0;
  left: 0;
}

.line.dark {
  background: rgba(255, 255, 255, 0.08);
}
</style>
