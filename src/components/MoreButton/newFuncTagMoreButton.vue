<template>
  <view class="container" @click="moreClick">
    <view>
      <MoreButton :theme="theme" @onMoreClick="moreClick"></MoreButton>
      <image v-if="showTag" class="tag" :src="newFuncTag"></image>
    </view>
  </view>
</template>

<script>
import newFuncTag from '@/assets/images/new_func_tag.png';
import MoreButton from '@/components/MoreButton/index.vue';

export default {
  props: {
    showTag: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: '',
    },
  },
  components: {
    MoreButton
  },
  data() {
    return {
      newFuncTag
    };
  },
  methods: {
    moreClick() {
      this.$emit('onMoreClick');
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag {
  position: absolute;
  top: 5;
  right: -50;
  height: 16;
  width: 50;
}
</style>
