<template>
  <k-view @click="moreClick" class="more-container">
    <k-text :class="['more', theme]">{{ title }}</k-text>
    <k-image class="right" :src="arrowIconSrc"></k-image>
  </k-view>
</template>

<script>
import arrowIcon from '@/assets/images/more_arrow.png';
import arrowIconDark from '@/assets/images/more_arrow_dark.png';

export default {
  props: {
    title: {
      type: String,
      default: '查看更多',
    },
    theme: {
      type: String,
      default: '',
    },
  },
  computed: {
    arrowIconSrc() {
      return this.theme === 'dark' ? arrowIconDark : arrowIcon;
    },
  },
  methods: {
    moreClick() {
      this.$emit('onMoreClick');
    },
  },
};
</script>

<style lang="less" scoped>
.more-container {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 42;
}

.more {
  font-size: 14;
  color: rgba(0, 0, 0, 0.60);
  text-align: right;
}

.more.dark {
  color: rgba(255, 255, 255, 0.60);
}

.right {
  width: 12;
  height: 12;
}
</style>
