import { FSRelevantFundHummerViewModel } from '@hummer/hummer-front';
import { Base } from '@king-fisher/tenon-vue';

export class HomeViewModel extends Base {
  refreshCallback = null;
  constructor() {
    super();
    if (__GLOBAL__.isIos) {
      this.element = new FSRelevantFundHummerViewModel();
    } else {
      this.element = {};
    }
  }
  initHummerContext(hummerContainerId) {
    //将当前句柄传递给原生端
    this.element.initHummerContext && this.element.initHummerContext(this, hummerContainerId);
  }

  viewHeightDidChanged(height) {
    this.element.viewHeightDidChanged && this.element.viewHeightDidChanged(height);
  }

  hummerModuleNeedRefresh() {
    this.refreshCallback && this.refreshCallback();
  }

  setRefeshCallback(callback) {
    this.refreshCallback = callback;
  }
}
