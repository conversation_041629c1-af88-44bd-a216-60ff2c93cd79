<template>
  <k-view class="cell">
    <k-view class="cell-item item-1">
      <k-text textLineClamp="1" :class="['cell-item-text', 'font-bold', theme]">{{ item.name }}</k-text>
      <k-view class="cell-left-sub">
        <k-text v-if="item.tag.length" textLineClamp="1" class="cell-item-tag">{{ item.tag }}</k-text>
        <k-text :class="['cell-item-second', theme]" textLineClamp="1" style="color: #FF661A;"> {{ item.description }}
        </k-text>
      </k-view>
    </k-view>
    <k-view class="cell-item item-2">
      <k-text :style="'color:' + riseColor" class="cell-item-text ta-r rise-font">{{
          item.rise.value
      }}
      </k-text>
      <k-text style="margin-top: 8px;" :class="['cell-item-second ta-r', theme]">{{ item.duration }}</k-text>
    </k-view>
    <k-view :class="['line', theme]"></k-view>
  </k-view>
</template>

<script>
import { RecommendModel } from '@/lib/constant.js';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
    item: {
      type: RecommendModel,
      default: {},
    },
  },
  computed: {
    riseColor() {
      if (this.theme === 'dark') {
        return this.item.rise.darkValueColor;
      }
      return this.item.rise.valueColor;
    }
  }
};
</script>

<style lang="less" scoped>
@import url('@/assets/style/index.less');

.cell {
  width: 100%;
  height: 131px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.cell-left-sub {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8px;
  width: 100%;
}

.cell-item {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.cell-item-text {
  width: 100%;
  color: #000000D6;
  height: 24;
  font-size: 16;
}

.cell-item-text.dark {
  color: rgba(255, 255, 255, 0.84);
}

.cell-item-second {
  width: 100%;
  color: #00000066;
  font-size: 14;
  height: 18;
  flex-shrink: 1;
}

.cell-item-second.dark {
  color: #FFFFFF66;
}

.cell-item-tag {
  color: #FF661A;
  font-size: 11;
  height: 16;
  padding: 2 4;
  border: 0.5px solid #FF661A;
  text-align: center;
  border-radius: 2;
  margin-right: 4;
}

.item-1 {
  width: 65%;
  height: 100%;
}

.item-2 {
  width: 30%;
  height: 100%;
}

.font-bold {
  font-weight: bold;
}

.line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  bottom: 0;
}

.line.dark {
  background: rgba(255, 255, 255, 0.08);
}
</style>
