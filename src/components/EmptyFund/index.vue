<template>
  <k-view :class="['empty-container', theme]">
    <k-text :class="['title', theme]">{{ title }}</k-text>
    <k-view v-if="showMore" @click="moreClick" class="more-container">
      <k-text class="more">{{ moreText }}</k-text>
      <k-image class="right" :src="arrowIcon"></k-image>
    </k-view>
  </k-view>
</template>

<script>
import arrowIcon from '@/assets/images/arrow_orange.png';

export default {
  data() {
    return {
      arrowIcon,
    };
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    moreText: {
      type: String,
      default: '',
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: '',
    },
  },
  methods: {
    moreClick() {
      this.$emit('onEmptyClick');
    },
  },
};
</script>

<style lang="less" scoped>
.empty-container {
  width: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 16;
  color: rgba(0, 0, 0, 0.60);
}

.title.dark {
  color: rgba(255, 255, 255, 0.60);
}

.more {
  font-size: 14;
  color: #FF661A;
  margin-right: 4;
}

.more-container {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 6;
}

.right {
  width: 8;
  height: 8;
}
</style>
