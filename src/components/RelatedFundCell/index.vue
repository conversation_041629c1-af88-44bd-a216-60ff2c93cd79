<template>
  <k-view class="cell">
    <k-view class="cell-item item-1">
      <k-text minFontSize="10" textLineClamp="1" :class="['cell-item-text', theme]">{{ item.name }}</k-text>
      <k-text :class="['cell-item-desc', theme]">{{ item.code }}</k-text>
    </k-view>
    <k-view class="cell-item item-2">
      <k-text :style="'color:' + riseColor" class="cell-item-text ta-r rise-font">{{
          item.rise.value
      }}
      </k-text>
    </k-view>
    <k-view v-for="data, idx in headerList" :key="idx" class="cell-item item-3">
      <k-text minFontSize="10" textLineClamp="1" :style="getItemColorStyle(data.key)" :class="['cell-item-text', 'ta-r', 'rise-font', theme]">{{
          getCurrentItemValue(data.key)
      }}</k-text>
    </k-view>
    <k-view :class="['line', theme]"></k-view>
  </k-view>
</template>

<script>
import { ETFModel } from '@/lib/constant.js';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
    item: {
      type: ETFModel,
      default: {},
    },
    headerList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    riseColor() {
      if (this.theme === 'dark') {
        return this.item.rise.darkValueColor;
      }
      return this.item.rise.valueColor;
    }
  },
  methods: {
    getCurrentItemValue(key) {
      if ('premiumRate' === key) {
        return this.item.premiumRate.value;
      }

      return this.item[key];
    },
    getItemColorStyle(key) {
      if ('premiumRate' === key) {
        let color = '';
        if (this.theme === 'dark') {
          color = this.item.premiumRate.darkValueColor;
        } else {
          color = this.item.premiumRate.valueColor;
        }
        return { color };
      } else {
        return {};
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import url('@/assets/style/index.less');

.cell {
  width: 100%;
  height: 104px;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  position: relative;
}

.cell-item {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.cell-item-text {
  width: 100%;
  color: rgba(0, 0, 0, 0.84);
  height: 24;
  font-size: 16;
}

.cell-item-text.dark {
  color: rgba(255, 255, 255, 0.84);
}

.cell-item-desc {
  font-size: 12;
  color: rgba(0, 0, 0, 0.40);
}

.cell-item-desc.dark {
  color: rgba(255, 255, 255, 0.40);
}

.item-1 {
  width: 206px;
  height: 100%;
}

.item-2 {
  margin-left: 16px;
  width: 144px;
  height: 100%;
}

.item-3 {
  margin-left: 16px;
  width: 144px;
  height: 100%;
}

.font-bold {
  font-weight: bold;
}

.line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  bottom: 0;
  left: 0;
}

.line.dark {
  background: rgba(255, 255, 255, 0.08);
}
</style>
