<template>
  <k-view :class="['bottom-container', theme]">
    <k-view class="left line"></k-view>
    <k-text :class="['title', theme]">已经到底了</k-text>
    <k-view :class="['right', 'line', theme]"></k-view>
  </k-view>
</template>

<script>

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="less" scoped>
.bottom-container {
  width: 100%;
  height: 80px;
  background: #F5F5F5;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.bottom-container.dark {
  background: #0F0F0F;
}

.title {
  font-size: 12;
  height: 32px;
  color: #0000003D;
  text-align: center;
}

.title.dark {
  color: #ffffff3D;
}

.line {
  width: 80px;
  height: 1px;
  background: #00000014;
}

.line.dark {
  background: #ffffff14;
}

.left.line {
  margin-right: 12px;
}

.right.line {
  margin-left: 12px;
}
</style>
