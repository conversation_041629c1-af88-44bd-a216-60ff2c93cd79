<template>
  <k-view :class="['recommond-list-container', theme]">
    <k-image :src="bgIconSrc" :class="['background', theme]"></k-image>
    <k-view class="header">
      <k-image class="hot" :src="hotType === 'hot' ? hotIcon : goodIcon"></k-image>
      <k-text class="hot-text">{{ hotText }}</k-text>
    </k-view>
    <k-view class="list">
      <RecommondFundCell v-for="data, index in dataList" @click="onCellClick(index)" :theme="theme" :item="data"
        :key="index">
      </RecommondFundCell>
    </k-view>
    <MoreButton :title="moreTitle" :theme="theme" @onMoreClick="onMoreClick"></MoreButton>
  </k-view>
</template>

<script>
import hotIcon from '@/assets/images/hot.png';
import goodIcon from '@/assets/images/good.png';
import bgIcon from '@/assets/images/recommond_bg.png';
import bgDarkIcon from '@/assets/images/recommond_bg_dark.png';
import RecommondFundCell from '@/components/RecommondFundCell/index.vue';
import MoreButton from '@/components/MoreButton/index.vue';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
    hotText: {
      type: String,
      default: '',
    },
    hotType: {
      type: String,
      default: 'hot',
    },
    moreTitle: {
      type: String,
      default: '',
    },
    dataList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  components: {
    RecommondFundCell,
    MoreButton
  },
  data() {
    return {
      hotIcon,
      goodIcon,
    };
  },
  computed: {
    bgIconSrc() {
      return this.theme === 'dark' ? bgDarkIcon : bgIcon;
    },
  },
  mounted() {
    this.$emit('viewDidLoad');
  },
  methods: {
    onMoreClick() {
      this.$emit('onMoreClick');
    },
    onCellClick(index) {
      this.$emit('onCellClick', index);
    }
  },
};
</script>

<style lang="less" scoped>
.recommond-list-container {
  margin-bottom: 16;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1 12 0 rgba(149, 149, 149, 0.16);
  border-radius: 10px;
}

.recommond-list-container.dark {
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 5px;
  border-radius: 10px;
  background-color: #ffffff;
}

.background.dark {
  background-color: #1c1c1c;
}

.header {
  width: 100%;
  height: 60px;
  padding-left: 12;
  padding-right: 12;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 4;
}

.hot {
  width: 16;
  height: 16;
}

.hot-text {
  font-size: 14;
  color: #FF2436;
  font-weight: bold;
}

.list {
  flex-direction: column;
  width: 100%;
  padding-left: 12;
  padding-right: 12;
}
</style>
