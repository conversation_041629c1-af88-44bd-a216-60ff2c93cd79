<template>
  <k-view :class="['top-header', theme]">
    <k-view @click="rightClick" :class="['top-container', theme]">
      <k-image class="robot" :src="robotIcon"></k-image>
      <k-view class="title-container">
        <k-text textLineClamp="2" :class="['title', theme]">{{ query }}</k-text>
      </k-view>
      <k-image class="right" :src="arrowIconSrc"></k-image>
    </k-view>
  </k-view>
</template>

<script>
import robotIcon from '@/assets/images/ai_robot.gif';
import arrowIcon from '@/assets/images/container_more.png';
import arrowIconDark from '@/assets/images/container_more_dark.png';
import { store } from '@/lib/store';
import { EventTracingAction, sendEventCaptureRecord } from '@/utils/HXTools';
import lifeTimeMixin from '@/mixins/lifeTimeMixin';

export default {
  mixins: [lifeTimeMixin],
  data() {
    return {
      robotIcon,
    };
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: '',
    },
    config: {
      type: Object,
      default() {
        return { query: '', rec_info: '', trace_id: '' };
      },
    },
  },
  computed: {
    arrowIconSrc() {
      return this.theme === 'dark' ? arrowIconDark : arrowIcon;
    },
    query() {
      return this.config.query;
    },
    recInfo() {
      return this.config.rec_info;
    },
    traceId() {
      return this.config.trace_id;
    },
    jumpUrl() {
      let url = this.config.jump_url;
      if (url === undefined || url === null || url === '') {
        const urlPrefix = "client://client.html?action=ymtz^webid=2719^comefrom=ShouchaoHangqingJijinTab^type=1^entranceType=1^llmState=1";
        url = `${urlPrefix}^query=${this.query}^datajson={"traceId":"${this.traceId}"}`;
      }
      return url;
    }
  },
  methods: {
    rightClick() {
      this.sendCbasData(EventTracingAction.click);
      HXJsBridge.jumpNativePage(this.jumpUrl);
    },
    viewDidShow() {
      this.sendCbasData(EventTracingAction.show);
    },
    sendCbasData(action) {
      sendEventCaptureRecord('ths_mob_newfenshi_fundtab_chatbar', action, {
          sector: `${store.stockCode},${store.marketId}`,
          recInfo: this.recInfo
      });
    },
  },
};
</script>

<style lang="less" scoped>
.top-header {
  width: 100%;
  min-height: 40;
  max-height: 62;
  background-color: #f5f5f5;
  margin-top: 8;
}

.top-header.dark {
  background-color: #000;
}

.top-container {
  flex: 1;
  background-color: #fff;
  margin-left: 6;
  margin-right: 6;
  padding-left: 8;
  padding-right: 8;
  border-radius: 4;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.top-container.dark {
  background-color: #1c1c1c;
}

.title-container {
  flex: 1;
  padding-top: 9;
  padding-bottom: 9;
  margin-left: 8;
  margin-right: 8;
}

.robot {
  align-self: flex-start;
  margin-top: 6;
  width: 26;
  height: 24;
}

.right {
  width: 16;
  height: 16;
}

.title {
  flex: 1;
  font-size: 14;
  color: rgba(0, 0, 0, 0.84);
  font-weight: 400;
  line-height: 22;
  text-overflow: ellipsis;
}

.title.dark {
  color: #FFFFFFD6;
}
</style>
