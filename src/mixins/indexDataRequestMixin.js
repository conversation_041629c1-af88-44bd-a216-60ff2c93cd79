import { HxError, getHummerValueColor, getHummerZdfColor, getCookie, getUUID, isPlate } from '@/lib/tools.js';
import Apis from '@king-fisher/apis';
import { ETFModel, RecommendModel, FundTabAIMEScene } from '@/lib/constant.js';
import { store } from '@/lib/store';
import { getOuterFundUrl } from '@/lib/common';
import { fix2DicimalPercent, fix4Dicimal, formatAmount, fixDecimals } from '@/components/hqTable/hqTool';
import { getUserId } from '@/utils/HXTools';
import { FuturesSceneSecondType, SceneFirstType } from '@/lib/SceneTypeDefines';
import { isObject } from 'lodash';

export default {
  data: {},

  methods: {
    requestAIData() {
      const url = `${__GLOBAL__.url.AIRecommend}`;
      Apis.request({
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': getCookie()
        },
        url,
        data: {
          userId: getUserId(),
          scene: FundTabAIMEScene,
          stock: `${store.stockCode},${store.marketId}`,
          traceId: getUUID()
        },
        success: res => {
          if (res.data.status_code === 0) {
            this.AIConfig = res.data.data;
            this.refreshViewHeight && this.refreshViewHeight();
          }
        },
      });
    },
    // 发起http请求
    requestPlateOutFund() {
      if (!this.outerRelatedModuleShow) {
        return;
      }

      const urlPrefix = getOuterFundUrl(store.marketId);
      const url = `${urlPrefix}${this.outFundRequestParamString()}`;
      Apis.request({
        url,
        method: 'GET',
        success: res => {
          const handleData = this.parseHttpRes(res);
          if (handleData.flag === 0) {
            this.relatedOuterList = handleData.data.list;
            //matchType为high mid low，当matchType返回不为high的时候展示弱相关提示
            this.showWeakTips = (handleData.data.matchType != null) && handleData.data.matchType !== 'high';
            this.matchType = handleData.data.matchType;
            this.showOuterRelated = this.relatedOuterList.length > 0;
            this.refreshViewHeight && this.refreshViewHeight();
            if (!this.showOuterRelated) {
              this.recommendOuterRequest();
            }
          } else {
            this.recommendOuterRequest();
          }
        },
        fail: res => {
          this.recommendOuterRequest();
          throw new HxError('严选好基请求失败', res);
        },
      });
    },
    outFundRequestParamString() {
      let urlParams = `?code=${store.stockCode}&market=${store.marketId}&rowCount=5`
      if (isPlate(store.marketId) || store.firstSceneType === SceneFirstType.hkPlate) {
        // 后面追加
        urlParams += `&sortId=block_fund_holdrate&sortOrder=0`
      } else {
        urlParams += `&sortId=tmonth`
      }
      return urlParams;
    },

    recommendInnerRequest() {
      this.showInnerRecommend = true;
      const url = __GLOBAL__.url.HotETF;
      Apis.request({
        url,
        method: 'GET',
        success: res => {
          const { list } = res.data.data;
          const recommendList = [];
          const maxCount = 3;
          for (let index = 0; index < list.length; index++) {
            if (index >= maxCount) {
              break;
            }
            const element = list[index];
            const recommend = new RecommendModel();
            recommend.name = element.name;
            recommend.code = element.code;
            recommend.marketId = element.market;
            const unit = 10000.0;
            const places = 2;
            const rate = fixDecimals(element.rate / unit, places);
            recommend.description = `${rate}w人搜索访问`;
            recommend.duration = '近一周涨幅';
            recommendList.push(recommend);
          }
          //获取推荐列表
          this.recommendInnerList = recommendList;
          //iOS端下需刷新视图高度
          this.refreshViewHeight && this.refreshViewHeight();
          //获取列表后再获取涨跌幅数据
          this.fetchInnerRecommendHQ();
        },
        fail: res => {
          throw new HxError('热门ETF请求失败', res);
        },
      });
    },

    recommendOuterRequest() {
      this.showOuterRecommend = true;
      const url = __GLOBAL__.url.YanXuanFund;
      Apis.request({
        url,
        method: 'GET',
        success: res => {
          try {
            if (res.data.status_code !== 0) {
              this.refreshViewHeight && this.refreshViewHeight();
              return;
            }
            const list = JSON.parse(res.data.data);
            const recommendList = [];
            const maxCount = 3;
            for (let index = 0; index < list.tuijianList.length; index++) {
              if (index >= maxCount) {
                break;
              }
              const element = list.tuijianList[index];
              const recommend = new RecommendModel();
              recommend.name = element.fundName;
              recommend.code = element.fundCode;
              recommend.tag = element.title;
              recommend.description = element.cptjTitle;
              recommendList.push(recommend);
            }
            this.recommendOuterList = recommendList;
            this.recommendOuterRiseRequest();
            this.refreshViewHeight && this.refreshViewHeight();
          } catch (error) {
            throw new HxError('严选好基解析失败', error);
          }
        },
        fail: res => {
          this.refreshViewHeight && this.refreshViewHeight();
          throw new HxError('严选好基请求失败', res);
        },
      });
    },
    //获取场外的涨跌幅
    recommendOuterRiseRequest() {
      const codeList = [];
      this.recommendOuterList.forEach(element => {
        codeList.push(element.code);
      });
      const url = __GLOBAL__.url.YanXuanFundZDF;
      const params = {
        cardList: [
          {
            cardEnum: 'RATE_BASIC_DATA_V1',
            ext: {
              codeList,
            },
          },
        ],
      };
      //threeMonthRate halfYearRate yearRate twoYearRate threeYearRate
      const priority = [
        'threeYearRate',
        'twoYearRate',
        'yearRate',
        'halfYearRate',
        'threeMonthRate',
        'nowRate',
      ];
      const priorityString = [
        '近三年收益',
        '近两年收益',
        '近一年收益',
        '近半年收益',
        '近三个月收益',
        '成立以来收益',
      ];
      Apis.request({
        url,
        method: 'POST',
        data: params,
        success: res => {
          const { basicDataMap } = res.data.data.RATE_BASIC_DATA_V1;
          this.recommendOuterList.forEach(model => {
            const data = basicDataMap[model.code];
            for (let index = 0; index < priority.length; index++) {
              const element = priority[index];
              if (data[element]) {
                model.rise = this.parseRiseColor(
                  data,
                  element,
                  fix2DicimalPercent,
                  getHummerZdfColor
                );
                model.duration = priorityString[index];
                break;
              }
            }
          });
        },
        fail: res => {
          this.refreshViewHeight && this.refreshViewHeight();
          throw new HxError('严选好基涨跌幅请求失败', res);
        },
      });
    },

    // 解析http返回数据
    parseHttpRes(res) {
      const resData = res?.data?.data;
      let table;
      let matchType;
      if (resData && Object.keys(resData).length > 0) {
        const codeData = resData[store.stockCode];
        if (codeData.table) {
          table = codeData.table;
          matchType = codeData.info.matchType;
        }
      }
      if (!table) {
        // 解析失败
        return {
          handled: true,
          flag: -1,
        };
      }
      const { total, data } = table;
      if (!data) {
        // 解析失败
        return {
          handled: true,
          flag: -1,
        };
      }
      const list = this.parseDataArr(data);
      return {
        handled: true,
        flag: 0,
        data: {
          startIndex: 0,
          matchType,
          total,
          list,
        },
      };
    },
    // 解析数据列表
    parseDataArr(data) {
      const rowList = [];
      data.forEach(element => {
        const rowData = this.parseRowData(element);
        rowList.push(rowData);
      });
      return rowList;
    },

    // 解析单行数据
    parseRowData(rowData) {
      const model = new ETFModel();
      model.name = rowData.name;
      model.code = rowData.code;
      model.marketId = rowData.market;
      let riseKey = "tmonth"
      if (store.firstSceneType === SceneFirstType.futures || store.firstSceneType === SceneFirstType.spot || store.firstSceneType === SceneFirstType.hkPlate) {
        if (store.secondSceneType === FuturesSceneSecondType.bondFutures) {
          riseKey = "year"
        }
      }
      model.rise = this.parseRiseColor(
        rowData,
        riseKey,
        fix2DicimalPercent,
        getHummerZdfColor
      );

      const fundScale = this.parseNormalColor(
        rowData,
        'fundScale',
        formatAmount,
        getHummerValueColor
      );
      model.scale = fundScale.value;

      const rate = this.parseNormalColor(
        rowData,
        'fundInvestRate',
        fix2DicimalPercent,
        getHummerValueColor
      );
      model.positionRatio = rate.value;

      const similarity = this.parseNormalColor(
        rowData,
        'fundPerformanceSimilarity',
        fix2DicimalPercent,
        getHummerValueColor
      );
      model.similarity = similarity.value;

      // 净值。指数相关场外基金需读取
      const unitNav = this.parseNormalColor(
        rowData,
        'unitNav',
        fix4Dicimal,
        getHummerValueColor
      );
      model.unitNav = unitNav.value;

      return model;
    },
  },
};
