import { isPlate } from '@/lib/tools';
import { isValidSrting } from '@/utils/commonTools.ts';
import { PageLevel, HttpDataFieldMap } from '@/lib/constant';
import { SceneFirstType } from '@/lib/SceneTypeDefines';

const FirstPageRelationList = ['COOPERATE_ETF', 'RELATED_ETF'];
const SecondPageRelationList = ['RELATED_ETF'];

const FirstPageIndexParamTypeList = [];
const FirstPagePlateParamTypeList = [HttpDataFieldMap.similar, HttpDataFieldMap.investRate];
const FirstPageHkAndUsStockParamTypeList = [HttpDataFieldMap.investRate];
const FirstPageHKPlateParamTypeList = [HttpDataFieldMap.heat, HttpDataFieldMap.operFee, HttpDataFieldMap.scale]
const SecondPageParamTypeList = [
  HttpDataFieldMap.heat,
  HttpDataFieldMap.operFee,
  HttpDataFieldMap.scale,
  HttpDataFieldMap.similar,
  HttpDataFieldMap.investRate
];

const FIRST_PAGE_MAX_COUNT = 4;

export default {
  data: {},
  methods: {
    getRelatedInnerFundHttpRequestParams(marketId, code, pageLevel, sceneType) {
      const isSecondPage = pageLevel === PageLevel.level2;
      const plateFlag = isPlate(marketId);

      let typeList;
      if (isSecondPage) {
        typeList = SecondPageParamTypeList;
      } else {
        // 一级页
        typeList = this.getFirstPageTypeList(sceneType, plateFlag);
      }

      let type;
      if (sceneType === SceneFirstType.hkStock || sceneType === SceneFirstType.usStock) {
        type = 'stock';
      } else if (sceneType === SceneFirstType.futures) {
        type = 'future'
      } else if (sceneType === SceneFirstType.spot) {
        type = 'spot'
      } else if (sceneType === SceneFirstType.hkPlate) {
        type = 'plate_HK'
      } else {
        type = plateFlag ? 'plate' : 'index';
      }

      return {
        thscode: {
          marketId,
          code,
          type,
        },
        relationList: isSecondPage ? SecondPageRelationList : FirstPageRelationList,
        typeList,
        limit: isSecondPage ? null : FIRST_PAGE_MAX_COUNT,
      };
    },
    getFirstPageTypeList(sceneType, plateFlag) {
      let typeList = [];
      if (sceneType === SceneFirstType.hkStock || sceneType === SceneFirstType.usStock) {
        typeList = FirstPageHkAndUsStockParamTypeList;
      } else if (sceneType === SceneFirstType.hkPlate) {
        typeList = FirstPageHKPlateParamTypeList;
      } else if (plateFlag) {
        typeList = FirstPagePlateParamTypeList;
      } else {
        typeList = FirstPageIndexParamTypeList;
      }
      return typeList;
    },

    getRelatedInnerFundTcpRequestParams(fieldList, fundList, pageLevel) {
      const codeMap = {};
      const fieldListStr = fieldList.join(',');

      fundList.forEach(item => {
        if (!isValidSrting(item.code)) {
          return;
        }
        const codeList = codeMap[item.marketId] || [];
        codeList.push(item.code);

        codeMap[item.marketId] = codeList;
      });

      let codeListParamStr = '';
      Object.keys(codeMap).forEach(marketId => {
        const codes = codeMap[marketId].join(',');
        codeListParamStr = `${codeListParamStr}${marketId}(${codes},);`;
      });

      const isFirstPage = pageLevel === PageLevel.level1;
      const pageId = isFirstPage ? '2267' : '2571';
      const pageName = isFirstPage ? 'RelatedFund' : 'plateInnerFund';

      return {
        protocolId: '4106',
        pageId,
        onlineId: pageName,
        requestDic: `codelist=${codeListParamStr}\r\ndataitem=${fieldListStr}\r\npush=1\r\nscenario=${pageName}\r\n`,
      };
    },
  },
};
