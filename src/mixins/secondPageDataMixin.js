import { store } from '@/lib/store';
import { isPlate } from '@/lib/tools';
import { defineComponent } from '@king-fisher/app-vue';
import { PageLevel, HttpTitleFieldMap, HttpDataFieldMap, TcpFieldMap, TitleFieldDataKeyMap } from '@/lib/constant.js';
import { disConnectHQ } from '@/lib/common.ts';
import { isValidArray } from '@/utils/commonTools';
import requestMixin from '@/mixins/requestMixin';
import Apis from '@king-fisher/apis';
import { getValueColor, getSubValueColor, formatValue, getCellColor } from '@/components/hqTable/hqTool';
import { isNull } from '@/utils/commonTools.ts';
import { SceneFirstType } from '@/lib/SceneTypeDefines';

export default defineComponent({
  created() {
    this.secondPagLiveDataBridge = new UnifiedRequestBridge();
  },
  onUnload() {
    disConnectHQ(this.secondPagLiveDataBridge);
  },
  mixins: [requestMixin],
  data: {
    secondPageLiveDataBridge: null,
    secondPageRelatedFunds: [],
    secondRowDataList: [],
    secondPageTableData: {
      startIndex: 0,
      total: 0,
      list: [],
    },
    secondPageSortId: '',
    secondPageSortOrder: ''
  },
  computed: {
    isPlate() {
      return isPlate(store.marketId);
    },
  },
  methods: {
    requestSeconedPageData(sortId, sortOrder) {
      this.secondPageSortId = sortId;
      this.secondPageSortOrder = sortOrder;

      const params = this.getRelatedInnerFundHttpRequestParams(store.marketId, store.stockCode, PageLevel.level2, store.firstSceneType);
      Apis.request({
        url: __GLOBAL__.url.RelatedInnerFund,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        data: params,
        success: res => {
          const result = this.parseSecondPagedData(res);
          if (result) {
            this.fetchSecondPagLiveData();
          }
        },
        fail: res => {
          this.handleErrorResult();
        },
      });
    },
    parseSecondPagedData(res) {
      if (res?.data?.status_code !== 0) {
        this.handleErrorResult();
        return false;
      }
      if (!isValidArray(res?.data?.data?.fundList)) {
        this.handleEmptyResult();
        return false;
      }

      const data = res?.data?.data;
      this.secondPageTableData.total = data?.total || 0;
      this.secondPageRelatedFunds = data?.fundList;

      return true;
    },

    fetchSecondPagLiveData() {
      disConnectHQ(this.secondPagLiveDataBridge);

      this.secondPagLiveDataBridge.init(
        JSON.stringify(this.getSecondPageHQParams()),
        res => {
          if (res.head.errorCode === 0 || res.head.errorCode === '0') {
            this.parseSecondPagLiveData(res);
          } else {
            this.handleErrorResult();
          }
        }
      );
    },
    getSecondPageHQParams() {
      const fundList = this.secondPageRelatedFunds.map(item => {
        return {
          code: item.fundCode,
          marketId: item.marketId
        };
      });

      const tcpParams = this.getRelatedInnerFundTcpRequestParams(
        [
          TcpFieldMap.rise,
          TcpFieldMap.tradeVol,
          TcpFieldMap.premiumRate,
          TcpFieldMap.latestPrice,
          TcpFieldMap.week,
          TcpFieldMap.month,
          TcpFieldMap.threeMonth,
          TcpFieldMap.halfYear,
          TcpFieldMap.year
        ],
        fundList,
        PageLevel.level2
      );
      // tcp字段支持排序，增加sortid查询
      if (!this.isHttpField) {
        tcpParams.requestDic = `${tcpParams.requestDic}sortid=${this.secondPageSortId}\r\nsortorder=${this.secondPageSortOrder}\r\n`;
      }

      return tcpParams;
    },
    parseSecondPagLiveData(res) {
      const {
        [TcpFieldMap.name]: nameArray = [],
        [TcpFieldMap.code]: codeArray = [],
        [TcpFieldMap.rise]: riseArray = [],
        [TcpFieldMap.tradeVol]: tradeVolumeArray = [],
        [TcpFieldMap.premiumRate]: premiumRateArray = [],
        [TcpFieldMap.latestPrice]: latestPriceArray = [],
        [TcpFieldMap.week]: weekRateArray = [],
        [TcpFieldMap.month]: monthRateArray = [],
        [TcpFieldMap.threeMonth]: threeMonthRateArray = [],
        [TcpFieldMap.halfYear]: halfYearRateAarray = [],
        [TcpFieldMap.year]: yearRateArray = [],
      } = res.body.dataDict;

      this.secondRowDataList = [];
      this.secondPageTableData.list = [];
      codeArray.forEach((code, index) => {
        const fundItem = this.secondPageRelatedFunds.find(item => item.fundCode === code);

        const rowData = {
          name: nameArray[index],
          code,
          market: fundItem.marketId,
          rise: riseArray[index],
          tradeVol: tradeVolumeArray[index],
          premiumRate: premiumRateArray[index],
          heat: fundItem.typeData[HttpDataFieldMap.heat],
          scale: fundItem.typeData[HttpDataFieldMap.scale],
          operFee: fundItem.typeData[HttpDataFieldMap.operFee],
          latest: latestPriceArray[index],
          week: weekRateArray[index],
          month: monthRateArray[index],
          tmonth: threeMonthRateArray[index],
          hyear: halfYearRateAarray[index],
          year: yearRateArray[index]
        };
        if (store.firstSceneType === SceneFirstType.hkStock || store.firstSceneType === SceneFirstType.usStock) {
          rowData['investRate'] = fundItem.typeData[HttpDataFieldMap.investRate];
        } else if (this.isPlate || store.firstSceneType === SceneFirstType.hkPlate) {
          rowData['similar'] = fundItem.typeData[HttpDataFieldMap.similar];
          rowData['investRate'] = fundItem.typeData[HttpDataFieldMap.investRate];
        } else {
          // 其他情况无需处理
        }
        this.secondRowDataList.push(rowData);

        const rowModel = this.parseRowData(rowData, this.secondPageTableData.startIndex + index);
        this.secondPageTableData.list.push(rowModel);
      });

      // tcp推送时，如果是http表头排序，需要自己处理
      if (this.isHttpField) {
        this.httpFieldSortAction(this.secondPageSortId, this.secondPageSortOrder, false);
      }

      this.handleParseResult({
        handled: true,
        flag: 0,
        data: JSON.parse(JSON.stringify(this.secondPageTableData)),
      });
    },

    parseRowData(rowData, rowIndex) {
      const cellList = [];

      // 名称代码
      cellList.push({
        value: rowData.name,
        subValue: rowData.code,
        valueColor: getValueColor(this.isDarkTheme),
        subValueColor: getSubValueColor(this.isDarkTheme),
        marketId: rowData.market,
      });
      // 数据列项
      this.headerFieldIds.forEach(field => {
        cellList.push(this.parseCellData(rowData, TitleFieldDataKeyMap[field] ,field));
      });

      return {
        index: rowIndex,
        stockCode: rowData.code,
        id: rowData.market,
        value: cellList,
      };
    },

    // 解析cell数据
    parseCellData(rowData, keyToGet, fieldId) {
      const value = rowData[keyToGet];
      return {
        value: isNull(value) ? '--' : formatValue(`${value}`, fieldId),
        valueColor: getCellColor(`${value}`, fieldId, this.isDarkTheme),
      };
    },

    handleErrorResult() {
      this.handleParseResult({
        handled: true,
        flag: -1,
        data: null,
      });
    },
    handleEmptyResult() {
      this.handleParseResult({
        handled: true,
        flag: 0,
        data: null,
      });
    },

    httpFieldSortAction(sortId, sortOrder, refresh = true) {
      this.secondPageSortId = sortId;
      this.secondPageSortOrder = sortOrder;

      const isAsc = sortOrder === '1';
      const sortKey = Object.entries(HttpTitleFieldMap).find(([key, value]) => value === sortId)?.[0] || '';

      const groupDataList = this.secondRowDataList.map((item, index) => {
        return {
          rowData: item,
          rowModel: this.secondPageTableData.list[index]
        };
      });

      const sortGroupDataList = groupDataList.sort((group1, group2) => {
        const defaultValue = isAsc ? Infinity : -Infinity;
        const value1 = group1.rowData[sortKey] ?? defaultValue;
        const value2 = group2.rowData[sortKey] ?? defaultValue;

        if (isAsc) {
          return value1 - value2;
        } else {
          return value2 - value1;
        }
      });

      this.secondRowDataList = sortGroupDataList.map(item => item.rowData);
      this.secondPageTableData.list = sortGroupDataList.map(item => item.rowModel);

      if (refresh) {
        this.tableData.list = JSON.parse(JSON.stringify(this.secondPageTableData.list));
      }
    },
  }
});
