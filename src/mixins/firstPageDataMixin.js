import { ETFModel, PageLevel, HttpDataFieldMap, TcpFieldMap } from '@/lib/constant.js';
import { disConnectHQ } from '@/lib/common.ts';
import { isValidArray } from '@/utils/commonTools.ts';
import dataUtilsMixin from './dataUtilsMixin';
import requestMixin from '@/mixins/requestMixin';
import Apis from '@king-fisher/apis';
import { getHummerValueColor, getHummerZdfColor } from '@/lib/tools';
import { fix2DicimalPercent, formatAmount } from '@/components/hqTable/hqTool';
import { store } from '@/lib/store';
import { SceneFirstType } from '@/lib/SceneTypeDefines';

export default {
  created() {
    this.firstPagLiveDataBridge = new UnifiedRequestBridge();
  },
  onUnload() {
    disConnectHQ(this.firstPagLiveDataBridge);
  },
  mixins: [requestMixin, dataUtilsMixin],
  data: {
    firstPagLiveDataBridge: null,
    firstPageRelatedFunds: [],
  },
  methods: {
    requestFirstPageData(marketId, stockCode, sceneType) {
      const params = this.getRelatedInnerFundHttpRequestParams(marketId, stockCode, PageLevel.level1, sceneType);
      Apis.request({
        url: __GLOBAL__.url.RelatedInnerFund,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        data: params,
        success: res => {
          // 一级页http接口解析结果
          const result = this.parseFirstPagedData(res, sceneType);

          if (result) {
            // 成功，继续请求tcp数据
            this.fetchFirstPagLiveData(sceneType);
          } else {
            // 失败，请求推荐数据
            this.startRecommendData();
          }
        },
        fail: res => {
          this.startRecommendData();
        },
      });
    },
    parseFirstPagedData(res, sceneType) {
      if (res?.data?.status_code !== 0 || !isValidArray(res?.data?.data?.fundList)) {
        return false;
      }

      const dataFundList = res?.data?.data?.fundList;
      const fundList = dataFundList.map(item => {
        const cellModel = new ETFModel();
        cellModel.code = item.fundCode;
        cellModel.marketId = item.marketId;

        if (sceneType === SceneFirstType.hkStock || sceneType === SceneFirstType.usStock) {
          cellModel.positionRatio = this.parseNormalColor(
            item.typeData,
            HttpDataFieldMap.investRate,
            fix2DicimalPercent,
            getHummerValueColor
          ).value;
        } else {
          if (this.isBanKuai || sceneType === SceneFirstType.hkPlate) {
            cellModel.similarity = this.parseNormalColor(
              item.typeData,
              HttpDataFieldMap.similar,
              fix2DicimalPercent,
              getHummerValueColor
            ).value;
            cellModel.positionRatio = this.parseNormalColor(
              item.typeData,
              HttpDataFieldMap.investRate,
              fix2DicimalPercent,
              getHummerValueColor
            ).value;
          }
        }

        return cellModel;
      });

      if (fundList.length !== this.firstPageRelatedFunds.length) {
        this.refreshViewHeight();
      }
      this.firstPageRelatedFunds = fundList;
      this.showInnerRelated = fundList.length > 0;

      return true;
    },

    fetchFirstPagLiveData(sceneType) {
      disConnectHQ(this.firstPagLiveDataBridge);

      this.firstPagLiveDataBridge.init(
        JSON.stringify(this.getFirstPageHQParams(sceneType)),
        res => {
          this.notifyViewDidShow();
          this.isInited = true;

          if (res.head.errorCode === 0 || res.head.errorCode === '0') {
            this.parseFirstPagLiveData(res, sceneType);
          }
        }
      );
    },
    getFirstPageHQParams(sceneType) {
      const indexFields = [TcpFieldMap.rise, TcpFieldMap.premiumRate, TcpFieldMap.tradeVol];
      let tcpFields;
      if (sceneType === SceneFirstType.hkStock || sceneType === SceneFirstType.usStock) {
        tcpFields = [TcpFieldMap.rise, TcpFieldMap.tradeVol];
      } else {
        tcpFields = this.isBanKuai ? [TcpFieldMap.rise] : indexFields;
      }
      return this.getRelatedInnerFundTcpRequestParams(
        tcpFields,
        this.firstPageRelatedFunds,
        PageLevel.level1
      );
    },
    parseFirstPagLiveData(res, sceneType) {
      const {
        [TcpFieldMap.name]: nameArray = [],
        [TcpFieldMap.code]: codeArray = [],
        [TcpFieldMap.rise]: riseArray = [],
        [TcpFieldMap.tradeVol]: tradeVolArray = [],
        [TcpFieldMap.premiumRate]: premiumRateArray = [],
      } = res.body.dataDict;

      codeArray.forEach((code, index) => {
        const targetItem = this.firstPageRelatedFunds.find(item => item.code === code);
        if (targetItem) {
          targetItem.name = nameArray[index] || '--';
          targetItem.rise = this.parseRiseColor(riseArray, index, fix2DicimalPercent, getHummerZdfColor);

          if (sceneType === SceneFirstType.hkStock || sceneType === SceneFirstType.usStock) {
            targetItem.tradingVolume = this.parseNormalColor(
              tradeVolArray,
              index,
              formatAmount,
              getHummerValueColor
            ).value;
          } else {
            if (this.isBanKuai === false) {
              targetItem.premiumRate = this.parseRiseColor(
                premiumRateArray,
                index,
                fix2DicimalPercent,
                getHummerZdfColor
              );
              targetItem.tradingVolume = this.parseNormalColor(
                tradeVolArray,
                index,
                formatAmount,
                getHummerValueColor
              ).value;
            }
          }
        }
      });
    },

    startRecommendData() {
      disConnectHQ(this.firstPagLiveDataBridge);

      this.recommendInnerRequest();
      this.notifyViewDidShow();
      this.isInited = true;
    },

    notifyViewDidShow() {
      if (!this.isInited && this.isNotifiedViewDidShow) {
        store.notifiViewDidShowEvent();
      }
    },
  }
};
