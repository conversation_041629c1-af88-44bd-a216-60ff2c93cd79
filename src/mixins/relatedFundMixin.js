import { HxError, getHummerZdfColor } from '@/lib/tools.js';
import { disConnectHQ } from '@/lib/common.ts';
import { fix2DicimalPercent } from '@/components/hqTable/hqTool';
import _ from 'lodash';

const CODE = '4';
const STOCKRISE = '33001';

//ETF一周涨幅
const ETFRISE = '34376';

export default {
  data: {
    innerRecommendRequestBridge: null,
    rowCount: 6,
    onlineId: 'RelatedFund',
  },
  computed: {},
  created() {
    this.innerRecommendRequestBridge = new UnifiedRequestBridge();
  },
  onUnload() {
    disConnectHQ(this.innerRecommendRequestBridge);
  },
  methods: {
    //获取推荐的场内基金涨跌幅
    fetchInnerRecommendHQ() {
      disConnectHQ(this.innerRecommendRequestBridge);
      this.innerRecommendRequestBridge.init(JSON.stringify(this.recommendHQParams()), res => {
        if (res.head.errorCode === 0 || res.head.errorCode === '0') {
          this.parseETFData(res);
        } else {
          throw new HxError('fetchInnerRecommendHQ Error', this.recommendHQParams());
        }
      });
    },
    // 4106 请求ETF的列表参数
    recommendHQParams() {
      const codeTable = {};
      this.recommendInnerList.forEach(item => {
        if (!item.code) {
          return;
        }
        const stockCode = item.code;
        let stockCodeList = codeTable[item.marketId];
        if (!stockCodeList) {
          stockCodeList = [];
        }
        stockCodeList.push(stockCode);
        codeTable[item.marketId] = stockCodeList;
      });
      let codeListParamStr = '';
      Object.keys(codeTable).forEach(marketId => {
        const codes = codeTable[marketId].join(',');
        codeListParamStr = `${codeListParamStr}${marketId}(${codes},);`;
      });
      const params = {
        protocolId: '4106',
        pageId: this.pageId,
        onlineId: 'hotRelatedFund',
        requestDic: `codelist=${codeListParamStr}\r\ndataitem=${STOCKRISE},${ETFRISE}\r\npush=1\r\nscenario=hotRelatedFund\r\n`,
      };
      return params;
    },
    parseETFData(res) {
      const { dataDict } = res.body;
      const etfCodeArray = _.get(dataDict, CODE, []);
      const etfRiseArray = _.get(dataDict, ETFRISE, []);

      for (let index = 0; index < etfCodeArray.length; index++) {
        const etfCode = etfCodeArray[index];
        for (let i = 0; i < this.recommendInnerList.length; i++) {
          const item = this.recommendInnerList[i];
          if (item.code === etfCode) {
            item.rise = this.parseRiseColor(etfRiseArray, i, fix2DicimalPercent, getHummerZdfColor);
            break;
          }
        }
      }
    },
  },
};
