import { store } from '@/lib/store';

export default {
  watch: {
    //向子视图发送通知
    themeState() {
      this.didChangedTheme && this.didChangedTheme(this.themeState);
    },
    //监听消息，触发事件
    viewDidShowEventState() {
      this.viewDidShow && this.viewDidShow();
    },
  },
  computed: {
    //关联store的状态
    themeState() {
      return store.theme;
    },
    //关联store的状态
    viewDidShowEventState() {
      return store.viewDidShowEventState;
    },
  },
  onUnload() {
    this.clear();
  },
  methods: {
    //修改store中的状态
    notifiViewDidShowEvent() {
      store.notifiViewDidShowEvent();
    },
    loadEventListener() {
      // 主题监听
      Hummer.notifyCenter.removeEventListener('ThemeVersionNotification');
      Hummer.notifyCenter.addEventListener('ThemeVersionNotification', this.getTheme);

      this.getTheme();
    },
    getTheme() {
      const systemInfo = HXJsBridge.getSystemInfo();
      const theme = systemInfo.theme ? 'dark' : '';
      this.theme = theme;
      store.changeTheme(theme);

      const blind = systemInfo.isColorBlindOn ? 'blind' : '';
      store.changeBlind(blind);
    },
    clear() {
      Hummer.notifyCenter.removeEventListener('ThemeVersionNotification');
    },
    isLimitedVersion() {
      return !_t.judgeVersion();
    },
  },
};
