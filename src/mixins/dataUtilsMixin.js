export default {
  data: {},

  methods: {
    // 解析cell数据
    parseRiseColor(rowData, keyToGet, formatValue, getColor) {
      const valueStr = `${rowData[keyToGet]}`;
      return {
        value: formatValue(valueStr),
        valueColor: getColor(valueStr, false),
        darkValueColor: getColor(valueStr, true),
      };
    },

    // 解析cell数据，数值不显示红绿色
    parseNormalColor(rowData, keyToGet, formatValue, getColor) {
      const valueStr = `${rowData[keyToGet]}`;
      return {
        value: formatValue(valueStr),
        valueColor: getColor(false),
        darkValueColor: getColor(true),
      };
    },
  },
};
