import { View, Text } from '@hummer/hummer-front';
import Apis from '@king-fisher/apis';

const BUTTON_LEFT_MARGIN = 8;
const BUTTON_WIDTH = 120;
const SINGLE_BUTTON_WIDTH = 240;

export default {
  computed: {
    // 弹窗背景色
    dialogBgColor() {
      return this.theme === 'dark' ? '#2B2B2B' : '#FFFFFF';
    },
    // 弹窗文字颜色
    dialogTextColor() {
      return this.theme === 'dark' ? '#FFFFFFD6' : '#000000D6';
    },
    // 弹窗文字颜色
    dialogSubTextColor() {
      return this.theme === 'dark' ? '#FFFFFF66' : '#00000066';
    },
    // 取消按钮背景色
    cancelButtonBgColor() {
      return this.theme === 'dark' ? '#FFFFFF0F' : '#0000000A';
    },
  },
  methods: {
    // 显示通知弹窗
    showDialog(title, content, subContent) {
      const contentView = new View();
      const text1 = new Text();
      text1.text = content;
      text1.style = {
        fontSize: 16,
        color: this.dialogTextColor,
        lineSpacingMulti: 1.25,
        marginBottom: 8,
      };

      contentView.appendChild(text1);
      if (subContent) {
        const text2 = new Text();
        text2.text = subContent;
        text2.style = {
          fontSize: 14,
          color: this.dialogSubTextColor,
          lineSpacingMulti: 1.3,
          marginBottom: 4,
        };
        contentView.appendChild(text2);
      }

      Apis.Confirm({
        title,
        content: contentView,
        showCancel: false,
        showButtonLine: false,
        confirmText: '我知道了',
        theme: '',
        styles: this.getDialogStyle(true),
      });
    },

    // 返回弹窗样式
    getDialogStyle(isSingleButton) {
      return {
        mainView: {
          backgroundColor: this.dialogBgColor,
          width: 280,
          paddingTop: 18,
        },
        title: {
          color: this.dialogTextColor,
          fontSize: 18,
          lineHeight: 20,
          fontWeight: 'bold',
          marginBottom: 12,
        },
        scrollView: {
          marginLeft: 16,
          marginRight: 16,
          paddingBottom: 0,
        },
        content: {
          color: this.dialogTextColor,
          fontSize: 16,
          textAlign: 'left',
          paddingBottom: 6,
          lineSpacingMulti: 1.25,
          marginBottom: 0,
        },
        buttonView: {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 76,
        },
        cancelText: {
          color: this.dialogTextColor,
          fontSize: 16,
          backgroundColor: this.cancelButtonBgColor,
          borderRadius: 4,
          borderRightWidth: 0,
          width: 120,
          height: 44,
        },
        confirmText: {
          color: '#ffffff',
          fontSize: 16,
          backgroundColor: '#ff2436',
          borderRadius: 4,
          marginLeft: isSingleButton ? 0 : BUTTON_LEFT_MARGIN,
          width: isSingleButton ? SINGLE_BUTTON_WIDTH : BUTTON_WIDTH,
          height: 44,
        },
      };
    },
  },
};
