/**
 * <AUTHOR>
 * @Date 2022-08-26 13:14:26
 * @LastEditTime 2022-08-31 13:47:48
 */
import Vue, { VNode } from 'vue';

declare global {
  namespace JSX {
    // tslint:disable no-empty-interface
    interface Element extends VNode {}
    // tslint:disable no-empty-interface
    interface ElementClass extends Vue {}
    interface IntrinsicElements {
      [elem: string]: any;
    }
  }

  const HXJsBridge: any;
}
