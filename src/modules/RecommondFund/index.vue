<template>
  <ModuleContainer :title="moduleName" :theme="theme" :moduleName="moduleName">
    <k-view class="recommond-container">
      <EmptyFund :title="emptyConfig.title" :showMore="emptyConfig.showMore" :moreText="emptyConfig.moreText"
        :theme="theme" @on-empty-click="onEmptyClick">
      </EmptyFund>
      <RecommondFundList v-if="dataList.length" :dataList="dataList" :moreTitle="moreTitle" :hotText="hotText"
        :hotType="hotType" :theme="theme" @on-cell-click="onCellClick"
        @on-more-click="onMoreClick">
      </RecommondFundList>
    </k-view>
  </ModuleContainer>
</template>

<script>
import robotIcon from '@/assets/images/ai_robot.gif';
import arrowIcon from '@/assets/images/container_more.png';
import ModuleContainer from '@/components/ModuleContainer/index.vue';
import RecommondFundList from '@/components/RecommondFundList/index.vue';
import EmptyFund from '@/components/EmptyFund/index.vue';
import lifeTimeMixin from '@/mixins/lifeTimeMixin';
import { store } from '@/lib/store';
import { ActionOperaType } from '@/lib/constant';
import { jumpToFenShi, jumpToNewWebPage } from '@/utils/HXTools';

const ETF_HOT_URL = 'https://eq.10jqka.com.cn/webpage/etf-ranking-list/index.html#/redirect';

export default {
  mixins: [lifeTimeMixin],
  props: {
    moduleName: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'inner',
    },
    statModuleName: {
      type: String,
      default: '',
    },
    hotText: {
      type: String,
      default: '',
    },
    moreTitle: {
      type: String,
      default: '',
    },
    emptyConfig: {
      type: Object,
      default() {
        return { title: '', showMore: false, moreText: '' };
      },
    },
    dataList: {
      type: Array,
      default() {
        return [];
      },
    },
    // 当前板块stockCode
    stockCode: {
      type: String,
      default: '',
    },
    // 当前用户可见板块的stockCode
    userVisibleStockCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      robotIcon,
      arrowIcon,
      theme: '',
      curVisibleStockCode: '',
    };
  },
  components: {
    ModuleContainer,
    EmptyFund,
    RecommondFundList,
  },
  watch: {
    userVisibleStockCode: {
      handler(visibleStockCode) {
        this.curVisibleStockCode = visibleStockCode;
        this.sendOnShowCbas(visibleStockCode);
      },
      immediate: true,
    },
  },
  computed: {
    hotType() {
      if (this.type === 'inner') {
        return 'hot';
      }
      return 'good';
    },
  },
  created() {
    this.theme = store.theme;
  },
  methods: {
    didChangedTheme(theme) {
      this.theme = theme;
    },
    viewDidShow() {
      this.sendOnShowCbas(this.curVisibleStockCode);
    },
    sendOnShowCbas(visibleStockCode) {
      if (!visibleStockCode) {
        return;
      }
      // 当前板块和当前用户可见板块不一致时，不发送埋点
      if (this.stockCode !== visibleStockCode) {
        return;
      }
      if (this.type === 'inner') {
        const cbas = `${this.statModuleName}.etfmodule.reserve.show`;
        _t.sendClientCbas(ActionOperaType.SHOW, cbas);
      } else {
        const cbas = `${this.statModuleName}.fundmodule.reserve.show`;
        _t.sendClientCbas(ActionOperaType.SHOW, cbas);
      }
    },
    onEmptyClick() {
      const cbas = `${this.statModuleName}.etfmodule.reserve.etfzone`;
      _t.sendClientCbas(ActionOperaType.CLICK, cbas);

      HXJsBridge.jumpNativePage(
        'client://client.html?action=ymtz^webid=2501'
      );
    },
    onCellClick(index) {
      const model = this.dataList[index];
      let cbas = '';
      if (this.type === 'inner') {
        cbas = `${this.statModuleName}.etfmodule.reserve.goetf.${index}`;
        const codeList = [];
        const marketList = [];
        const nameList = [];
        this.dataList.forEach(element => {
          codeList.push(element?.code);
          marketList.push(element?.marketId);
          nameList.push(element?.name);
        });
        jumpToFenShi(model.code, model.marketId, { codeList, marketList, nameList });
      } else {
        if (!this.isLimitedVersion()) {
          cbas = `${this.statModuleName}.fundmodule.reserve.gofund.${index}`;
          HXJsBridge.jumpNativePage(
            `client.html?action=ijijin^action=fund,code=${model.code}`
          );
        }
      }
      _t.sendClientCbas(ActionOperaType.JUMP, cbas, { toStockCode: model.code, toFrameId: this.pageId });
    },
    onMoreClick() {
      if (this.type === 'inner') {
        const cbas = `${this.statModuleName}.etfmodule.reserve.etfrank`;
        _t.sendClientCbas(ActionOperaType.CLICK, cbas);

        jumpToNewWebPage(ETF_HOT_URL);
      } else {
        if (!this.isLimitedVersion()) {
          const cbas = `${this.statModuleName}.fundmodule.reserve.more`;
          _t.sendClientCbas(ActionOperaType.CLICK, cbas);
          //跳转严选好基
          HXJsBridge.jumpNativePage(
            'client.html?action=ijijin^action=newWebpage,title=,url=https://fund.10jqka.com.cn/ifundapp_app/public/tzh/yanxuanFund/dist/index$ffcfa2.html,needHideNavigationBar='
          );
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.recommond-container {
  padding-left: 10;
  padding-right: 10;
}
</style>
