<template>
  <TopicView v-if="showTopicTips" :title="title" :theme="theme" @click="jumpToEducate"></TopicView>
</template>

<script lang="ts">
import { ActionOperaType } from '@/lib/constant';
import Apis from '@king-fisher/apis';
import { defineComponent } from '@king-fisher/app-vue';
import TopicView from '@/components/TopicView/index.vue';
import { appendUrlParams } from '@/lib/tools';
import { store } from '@/lib/store';
import { sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';

export default defineComponent({
  components: { TopicView },
  props: {
    theme: {
      type: String,
      default: '',
    },
    statModuleName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      configTitle: '',
      configUrl: '',
    };
  },
  computed: {
    title(): string {
      if (this.configTitle) {
        return this.configTitle;
      }
      return '';
    },
    moreUrl(): string {
      if (this.configUrl) {
        return this.configUrl;
      }
      return '';
    },
    showTopicTips() {
      return this.title && this.moreUrl && this.title.length > 0 && this.moreUrl.length > 0;
    }
  },
  mounted() {
    this.fetchFundTipConfig();
  },
  methods: {
    // 获取配置文案和链接
    fetchFundTipConfig() {
      const url = __GLOBAL__.url.fundTipConfig;
      Apis.request({
        url,
        method: 'GET',
        success: (res: any) => {
          const formDataList = res?.data?.status_code === 0 && res?.data?.data;
          if (!formDataList) {
            console.error('fetchFundTipConfig 解析失败 rawResData empty, rawResData', formDataList);
            return;
          }

          if (!formDataList || formDataList.length === 0) {
            console.error('fetchFundTipConfig 解析失败 formDataList == null or len is 0, formDataList', formDataList);
            return;
          }

          const matchList = formDataList.filter((element: any) => element?.data_code === 'zhishufenshichangwai' && element?.title && element?.url);

          if (matchList && matchList.length > 0) {
            this.configTitle = matchList[0].title;
            this.configUrl = matchList[0].url;
          } else {
            console.error('fetchFundTipConfig 解析失败 matchList empty ', matchList);
          }
        },
        fail: (res: any) => {
          console.error('fetchFundTipConfig 请求失败', res);
        },
      });
    },

    jumpToEducate() {
      sendEventCaptureRecord('ths_mob_newfenshi_relatedjj_neirong', EventTracingAction.click, {
        stock: `${store.stockCode}:${store.marketId}`,
        ...store.typeLogMap()
      });
      HXJsBridge.jumpNativePage(this.moreUrl);
    },
  },
});
</script>
