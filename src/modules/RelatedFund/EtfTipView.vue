<template>
  <TopicView :title="title" :theme="theme" @click="jumpToEducate"></TopicView>
</template>

<script lang="ts">
import { ActionOperaType } from '@/lib/constant';
import Apis from '@king-fisher/apis';
import { defineComponent } from '@king-fisher/app-vue';
import TopicView from '@/components/TopicView/index.vue';
import { appendUrlParams } from '@/lib/tools';
import { store } from '@/lib/store';
import { sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';

export default defineComponent({
  components: { TopicView },
  props: {
    theme: {
      type: String,
      default: '',
    },
    statModuleName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      configTitle: '',
      configUrl: '',
    };
  },
  computed: {
    title(): string {
      if (this.configTitle) {
        return this.configTitle;
      }
      return '像股票一样实时交易，无印花税';
    },
    moreUrl(): string {
      if (this.configUrl) {
        return this.configUrl;
      }
      return __GLOBAL__.url.EducateUrl;
    },
  },
  mounted() {
    this.fetchEtfTipConfig();
  },
  methods: {
    // 获取配置文案和链接
    fetchEtfTipConfig() {
      const url = __GLOBAL__.url.etfTipConfig;
      Apis.request({
        url,
        method: 'GET',
        success: (res: any) => {
          const rawResData = res?.data?.status_code === 0 && res?.data?.data;
          if (!rawResData) {
            console.error('fetchEtfTipConfig 解析失败 rawResData empty, rawResData', rawResData);
            return;
          }
          const resData = JSON.parse(rawResData);

          const formDataList = resData?.formData?.list;
          if (!formDataList || formDataList.length === 0) {
            console.error('fetchEtfTipConfig 解析失败 formDataList == null or len is 0, formDataList', formDataList);
            return;
          }

          const matchList = formDataList.filter((element: any) => element?.code === 'indexFund' && element?.urlName && element?.url);

          if (matchList && matchList.length > 0) {
            this.configTitle = matchList[0].urlName;
            this.configUrl = matchList[0].url;
          } else {
            console.error('fetchEtfTipConfig 解析失败 matchList empty ', matchList);
          }
        },
        fail: (res: any) => {
          console.error('fetchEtfTipConfig 请求失败', res);
        },
      });
    },

    jumpToEducate() {
      const cbas = `${this.statModuleName}.etfmodule.stydy.more`;
      _t.sendClientCbas(ActionOperaType.CLICK, cbas);
      sendEventCaptureRecord('ths_mob_newfenshi_relatedetf_neirong', EventTracingAction.click, {
        stock: `${store.stockCode}:${store.marketId}`,
        ...store.typeLogMap()
      });

      const webUrl = appendUrlParams(this.moreUrl, ['code', 'marketid'], [store.stockCode, store.marketId]);
      const clientUrl = `client://client.html?action=ymtz^mode=new^webid=2804^url=${webUrl}`;
      HXJsBridge.jumpNativePage(clientUrl);
    },
  },
});
</script>
