<template>
  <ModuleContainer :title="moduleName" @on-more-click="onRightClick" :theme="theme" :moreText="rightText"
    :showMore="showRightMore" :moduleName="moduleName">
    <k-view>
      <EtfTipViewVue v-if="showETFTopicTips" :theme="theme" :stat-module-name="statModuleName"></EtfTipViewVue>
      <FundTipView v-if="showFundTopicTips" :theme="theme" :stat-module-name="statModuleName"></FundTipView>
      <WeakRelatedTips v-if="showWeakTips" title="暂无与该板块/指数相关性强的场外基金" :theme="theme" @click="alertWeakTips">
      </WeakRelatedTips>
      <k-view v-if="!showWeakTips && type === 'outer'" style="height: 8; width: 100%;"></k-view>
      <RelatedFundList :type="type" :headerList="headerList" :dataList="dataList" @on-header-click="onHeaderClick" :theme="theme"
        @on-cell-click="onCellClick" @on-more-click="onMoreListClick"></RelatedFundList>
    </k-view>
  </ModuleContainer>
</template>

<script>
import robotIcon from '@/assets/images/ai_robot.gif';
import arrowIcon from '@/assets/images/container_more.png';
import ModuleContainer from '@/components/ModuleContainer/index.vue';
import RelatedFundList from '@/components/RelatedFundList/index.vue';
import WeakRelatedTips from '@/components/WeakRelatedTips/index.vue';
import EtfTipViewVue from './EtfTipView.vue';
import FundTipView from './FundTipView.vue';
import dialogMixin from '@/mixins/dialogMixin';
import { store } from '@/lib/store';
import lifeTimeMixin from '@/mixins/lifeTimeMixin';
import { ActionOperaType, headerSimilarity, headerPosition } from '@/lib/constant';
import { jumpToFenShi, sendEventCaptureRecord, EventTracingAction } from '@/utils/HXTools';

/// 发送埋点时当前是否有有效数据的标志
const hasValidDataFlag = "1";
const noValidDataFlag = "0";

export default {
  mixins: [dialogMixin, lifeTimeMixin],
  props: {
    moduleName: {
      type: String,
      default: '',
    },
    showWeakTips: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'inner',
    },
    statModuleName: {
      type: String,
      default: '',
    },
    pageId: {
      type: String,
      default: '',
    },
    matchType: {
      type: String,
      default: '',
    },
    rightText: {
      type: String,
      default: '',
    },
    headerList: {
      type: Array,
      default() {
        return [];
      },
    },
    dataList: {
      type: Array,
      default() {
        return [];
      },
    },
    // 当前板块stockCode
    stockCode: {
      type: String,
      default: '',
    },
    // 当前板块marketId
    market: {
      type: String,
      default: '',
    },
    // 当前用户可见板块的stockCode
    userVisibleStockCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      robotIcon,
      arrowIcon,
      theme: '',
      relatedStrength: 'strong',
      curVisibleStockCode: '',
    };
  },
  watch: {
    userVisibleStockCode: {
      handler(visibleStockCode) {
        this.curVisibleStockCode = visibleStockCode;
        this.sendOnShowCbas(visibleStockCode);
      },
      immediate: true,
    },
  },
  computed: {
    showRightMore() {
      return this.type === 'inner';
    },
    showETFTopicTips() {
      return this.type === 'inner';
    },
    showFundTopicTips() {
      return this.type === 'outer';
    }
  },
  components: {
    ModuleContainer,
    RelatedFundList,
    WeakRelatedTips,
    EtfTipViewVue,
    FundTipView,
  },
  mounted() {
    this.theme = store.theme;
  },
  methods: {
    didChangedTheme(theme) {
      this.theme = theme;
    },
    viewDidShow() {
      this.sendOnShowCbas(this.curVisibleStockCode);
    },
    sendOnShowCbas(curVisibleStockCode) {
      if (!this.dataList) {
        return;
      }
      if (!curVisibleStockCode) {
        return;
      }
      // 当前板块和当前用户可见板块不一致时，不发送埋点
      if (this.stockCode !== curVisibleStockCode) {
        return;
      }
      if (this.type === 'inner') {
        const cbas = `${this.statModuleName}.etfmodule.list.show`;
        _t.sendClientCbas(ActionOperaType.SHOW, cbas);

        const codeListStr = this.dataList.map((item) => (item.code + ':' + item.marketId)).join(',');
        sendEventCaptureRecord('ths_mob_newfenshi_timeshare_relatedetf', EventTracingAction.show, {
          stock: this.stockCode + ':' + this.market,
          code: codeListStr,
          result: this.dataList.length > 0 ? hasValidDataFlag : noValidDataFlag,
          ...store.typeLogMap()
        });
      } else {
        this.relatedStrength = this.matchType === 'high' ? 'strong' : 'little';
        const cbas = `${this.statModuleName}.fundmodule.${this.relatedStrength}.show`;
        _t.sendClientCbas(ActionOperaType.SHOW, cbas);

        const codeListStr = this.dataList.map((item) => (item.code + ':' + item.marketId)).join(',');
        sendEventCaptureRecord('ths_mob_newfenshi_timeshare_relatedfund', EventTracingAction.show, {
          stock: this.stockCode + ':' + this.market,
          fundCode: codeListStr,
          result: this.dataList.length > 0 ? hasValidDataFlag : noValidDataFlag,
          ...store.typeLogMap()
        });
      }
    },
    onRightClick() {
      const cbas = `${this.statModuleName}.etfmodule.list.etfzone`;
      _t.sendClientCbas(ActionOperaType.CLICK, cbas);

      sendEventCaptureRecord('ths_mob_newfenshi_relatedetf_etfzhuanqu', EventTracingAction.click, {
        stock: this.stockCode + ':' + this.market,
        ...store.typeLogMap()
      });

      HXJsBridge.jumpNativePage(
        'client://client.html?action=ymtz^webid=2501'
      );
    },
    alertWeakTips() {
      const cbas = `${this.statModuleName}.etfmodule.little.relate.click`;
      _t.sendClientCbas(ActionOperaType.CLICK, cbas);

      this.showDialog('强相关性基金', '当满足以下三者：\n1、有与板块/指数匹配的关键词\n2、持仓占比大于等于30%\n3、业绩相似度大于等于80%\n该基金判定为与板块具有强相关性。');
    },
    onHeaderClick(key) {
      const similarKey = headerSimilarity.key;
      const investRateKey = headerPosition.key;
      const similar = `${this.statModuleName}.etfmodule.list.similar.click`;
      const innerRatio = `${this.statModuleName}.etfmodule.list.ratio.click`;
      const outerRatio = `${this.statModuleName}.fundmodule.${this.relatedStrength}.ratio.click`;
      switch (key) {
        case similarKey:
          if (this.type === 'inner') {
            _t.sendClientCbas(ActionOperaType.CLICK, similar);
            this.showDialog('业绩相似度', '基金历史业绩走势和本板块走势的相似性；相似性越高，收益越接近本板块。');
          }
          break;
        case investRateKey:
          if (this.type === 'inner') {
            _t.sendClientCbas(ActionOperaType.CLICK, innerRatio);
          } else {
            _t.sendClientCbas(ActionOperaType.CLICK, outerRatio);
          }
          this.showDialog('持仓占比', '持仓占比=投资该板块相关成分股的持仓/基金总持仓。', '注意：ETF持仓占比每日更新，场外基金持仓占比跟随基金持仓披露日期更新');
          break;
        default:
          break;
      }
    },
    onCellClick(index) {
      const model = this.dataList[index];
      let cbas = '';
      if (this.type === 'inner') {
        cbas = `${this.statModuleName}.etfmodule.list.goetf.${index}`;
        const codeList = [];
        const marketList = [];
        const nameList = [];
        this.dataList.forEach(element => {
          codeList.push(element?.code);
          marketList.push(element?.marketId);
          nameList.push(element?.name);
        });

        sendEventCaptureRecord('ths_mob_newfenshi_relatedetf_etfcard', EventTracingAction.click, {
          code: model.code,
          marketId: model.marketId,
          stock: `${store.stockCode}:${store.marketId}`,
          ...store.typeLogMap()
        });

        jumpToFenShi(model.code, model.marketId, { codeList, marketList, nameList });
      } else {
        if (!this.isLimitedVersion()) {
          cbas = `${this.statModuleName}.fundmodule.${this.relatedStrength}.goetf.${index}`;

          sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_cardfund', EventTracingAction.click, {
            fundCode: model.code,
            marketId: model.marketId ?? "",
            stock: `${store.stockCode}:${store.marketId}`,
            ...store.typeLogMap()
          });

          HXJsBridge.jumpNativePage(
            `client.html?action=ijijin^action=fund,code=${model.code}`
          );
        }
      }
      _t.sendClientCbas(ActionOperaType.JUMP, cbas, { toStockCode: model.code, toFrameId: this.pageId });
    },
    onMoreListClick() {
      if (this.type === 'inner') {
        const cbas = `${this.statModuleName}.etfmodule.list.more`;
        _t.sendClientCbas(ActionOperaType.CLICK, cbas);

        sendEventCaptureRecord('ths_mob_newfenshi_relatedetf_more', EventTracingAction.click, { 
          stock: store.stockCode + ':' + store.marketId,
          ...store.typeLogMap()});

        HXJsBridge.jumpNativePage(
          `client://client.html?action=ymtz^webid=2571^stockcode=${store.stockCode}^marketid=${store.marketId}^stockname=${store.stockName}`
          + '^channelType=1'
        );
      } else {
        const cbas = `${this.statModuleName}.fundmodule.${this.relatedStrength}.more`;
        _t.sendClientCbas(ActionOperaType.CLICK, cbas);

        sendEventCaptureRecord('ths_mob_newfenshi_relatedfund_more', EventTracingAction.click, {
          stock: store.stockCode + ':' + store.marketId,
          ...store.typeLogMap()
        });

        HXJsBridge.jumpNativePage(
          `client://client.html?action=ymtz^webid=2571^stockcode=${store.stockCode}^marketid=${store.marketId}^stockname=${store.stockName}`
          + '^channelType=2'
        );
      }
    },
  },
};
</script>
