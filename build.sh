version=`git rev-parse --short HEAD`

cd dist

touch commitInfo.txt
echo ${version} > commitInfo.txt

ETFRoot='ETFRoot'
mkdir ${ETFRoot}
cp -r ./img ./${ETFRoot}

mkdir relatedETFTab
cp index.js ./relatedETFTab/relatedETFTab.js
cp index.js ./${ETFRoot}/relatedETFTab.js
mv index.js ./relatedETFTab.js
cp -r ./img ./relatedETFTab
zip -r ./relatedETFTab-${version}.zip  ./relatedETFTab
rm -rf relatedETFTab

mkdir relatedFunds
cp relatedFunds.js ./relatedFunds/relatedFunds.js
cp relatedFunds.js ./${ETFRoot}/relatedFunds.js
mv relatedFunds.js ./relatedFunds.js
cp -r ./img ./relatedFunds
zip -r ./relatedFunds-${version}.zip  ./relatedFunds
rm -rf relatedFunds

mv ./${ETFRoot} ./relatedETFTab-${version}